FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Test minimal requirements
COPY test_requirements.txt .
RUN pip install --no-cache-dir -r test_requirements.txt

# Install Playwright browsers
RUN playwright install chromium
RUN playwright install-deps chromium

CMD ["python", "-c", "print('Test successful!')"]
