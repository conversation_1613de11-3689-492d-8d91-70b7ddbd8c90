#!/usr/bin/env python3
"""
Test script to verify Multi-Agent Crawler Orchestrator installation
"""

import asyncio
import json
import time
from typing import Dict, Any

import httpx
from loguru import logger


class InstallationTester:
    """Test the installation of the Multi-Agent Crawler Orchestrator"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.gradio_url = "http://localhost:8001"
        self.nocodb_url = "http://localhost:8080"
        self.crawl4ai_url = "http://localhost:8002"
        
    async def test_service_health(self, url: str, service_name: str) -> bool:
        """Test if a service is healthy"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{url}/health")
                if response.status_code == 200:
                    logger.info(f"✅ {service_name} is healthy")
                    return True
                else:
                    logger.error(f"❌ {service_name} returned status {response.status_code}")
                    return False
        except Exception as e:
            logger.error(f"❌ {service_name} health check failed: {e}")
            return False
    
    async def test_api_endpoints(self) -> Dict[str, bool]:
        """Test main API endpoints"""
        results = {}
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                
                # Test status endpoint
                logger.info("🔍 Testing status endpoint...")
                try:
                    response = await client.get(f"{self.base_url}/api/v1/status")
                    results["status"] = response.status_code == 200
                    if results["status"]:
                        logger.info("✅ Status endpoint working")
                    else:
                        logger.error(f"❌ Status endpoint failed: {response.status_code}")
                except Exception as e:
                    logger.error(f"❌ Status endpoint error: {e}")
                    results["status"] = False
                
                # Test models endpoint
                logger.info("🔍 Testing models endpoint...")
                try:
                    response = await client.get(f"{self.base_url}/api/v1/models")
                    results["models"] = response.status_code == 200
                    if results["models"]:
                        logger.info("✅ Models endpoint working")
                    else:
                        logger.error(f"❌ Models endpoint failed: {response.status_code}")
                except Exception as e:
                    logger.error(f"❌ Models endpoint error: {e}")
                    results["models"] = False
                
                # Test search endpoint
                logger.info("🔍 Testing search endpoint...")
                try:
                    search_data = {
                        "query": "test search",
                        "num_results": 5,
                        "use_exa": False,  # Don't use real APIs in test
                        "use_brave": False
                    }
                    response = await client.post(
                        f"{self.base_url}/api/v1/search",
                        json=search_data
                    )
                    results["search"] = response.status_code in [200, 500]  # 500 expected without API keys
                    if results["search"]:
                        logger.info("✅ Search endpoint accessible")
                    else:
                        logger.error(f"❌ Search endpoint failed: {response.status_code}")
                except Exception as e:
                    logger.error(f"❌ Search endpoint error: {e}")
                    results["search"] = False
                
                # Test crawl endpoint
                logger.info("🔍 Testing crawl endpoint...")
                try:
                    crawl_data = {
                        "urls": ["https://httpbin.org/html"],
                        "extract_content": True
                    }
                    response = await client.post(
                        f"{self.base_url}/api/v1/crawl",
                        json=crawl_data,
                        timeout=60.0
                    )
                    results["crawl"] = response.status_code in [200, 500]  # May fail without proper setup
                    if results["crawl"]:
                        logger.info("✅ Crawl endpoint accessible")
                    else:
                        logger.error(f"❌ Crawl endpoint failed: {response.status_code}")
                except Exception as e:
                    logger.error(f"❌ Crawl endpoint error: {e}")
                    results["crawl"] = False
                
        except Exception as e:
            logger.error(f"❌ API testing error: {e}")
        
        return results
    
    async def test_crawl4ai_service(self) -> bool:
        """Test Crawl4AI service"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Test health
                health_response = await client.get(f"{self.crawl4ai_url}/health")
                if health_response.status_code != 200:
                    logger.error(f"❌ Crawl4AI health check failed: {health_response.status_code}")
                    return False
                
                # Test crawling
                crawl_data = {
                    "url": "https://httpbin.org/html",
                    "word_count_threshold": 5
                }
                crawl_response = await client.post(
                    f"{self.crawl4ai_url}/crawl",
                    json=crawl_data,
                    timeout=60.0
                )
                
                if crawl_response.status_code == 200:
                    result = crawl_response.json()
                    if result.get("success"):
                        logger.info("✅ Crawl4AI service working correctly")
                        return True
                    else:
                        logger.error(f"❌ Crawl4AI crawling failed: {result.get('error')}")
                        return False
                else:
                    logger.error(f"❌ Crawl4AI service failed: {crawl_response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Crawl4AI service test error: {e}")
            return False
    
    async def test_gradio_interface(self) -> bool:
        """Test if Gradio interface is accessible"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.gradio_url)
                if response.status_code == 200:
                    logger.info("✅ Gradio interface is accessible")
                    return True
                else:
                    logger.error(f"❌ Gradio interface failed: {response.status_code}")
                    return False
        except Exception as e:
            logger.error(f"❌ Gradio interface test error: {e}")
            return False
    
    async def test_nocodb_interface(self) -> bool:
        """Test if NocoDB interface is accessible"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.nocodb_url)
                if response.status_code == 200:
                    logger.info("✅ NocoDB interface is accessible")
                    return True
                else:
                    logger.error(f"❌ NocoDB interface failed: {response.status_code}")
                    return False
        except Exception as e:
            logger.error(f"❌ NocoDB interface test error: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests"""
        logger.info("🚀 Starting Multi-Agent Crawler Orchestrator Installation Tests")
        logger.info("=" * 70)
        
        results = {
            "timestamp": time.time(),
            "services": {},
            "api_endpoints": {},
            "interfaces": {},
            "overall_success": False
        }
        
        # Test service health
        logger.info("\n📋 Testing Service Health...")
        results["services"]["main_api"] = await self.test_service_health(
            self.base_url, "Main API"
        )
        results["services"]["crawl4ai"] = await self.test_service_health(
            self.crawl4ai_url, "Crawl4AI Service"
        )
        
        # Test API endpoints
        logger.info("\n📋 Testing API Endpoints...")
        results["api_endpoints"] = await self.test_api_endpoints()
        
        # Test Crawl4AI service functionality
        logger.info("\n📋 Testing Crawl4AI Service...")
        results["services"]["crawl4ai_functional"] = await self.test_crawl4ai_service()
        
        # Test interfaces
        logger.info("\n📋 Testing User Interfaces...")
        results["interfaces"]["gradio"] = await self.test_gradio_interface()
        results["interfaces"]["nocodb"] = await self.test_nocodb_interface()
        
        # Calculate overall success
        all_tests = []
        all_tests.extend(results["services"].values())
        all_tests.extend(results["api_endpoints"].values())
        all_tests.extend(results["interfaces"].values())
        
        success_rate = sum(all_tests) / len(all_tests) if all_tests else 0
        results["overall_success"] = success_rate >= 0.7  # 70% success rate
        results["success_rate"] = success_rate
        
        # Print summary
        logger.info("\n" + "=" * 70)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 70)
        
        logger.info(f"🎯 Overall Success: {'✅ PASS' if results['overall_success'] else '❌ FAIL'}")
        logger.info(f"📈 Success Rate: {success_rate:.1%}")
        
        logger.info("\n🔧 Service Health:")
        for service, status in results["services"].items():
            logger.info(f"  {service}: {'✅' if status else '❌'}")
        
        logger.info("\n🌐 API Endpoints:")
        for endpoint, status in results["api_endpoints"].items():
            logger.info(f"  {endpoint}: {'✅' if status else '❌'}")
        
        logger.info("\n🖥️ User Interfaces:")
        for interface, status in results["interfaces"].items():
            logger.info(f"  {interface}: {'✅' if status else '❌'}")
        
        if not results["overall_success"]:
            logger.info("\n⚠️ TROUBLESHOOTING TIPS:")
            logger.info("1. Check if all Docker containers are running: docker-compose ps")
            logger.info("2. Check container logs: docker-compose logs -f")
            logger.info("3. Verify API keys are set in .env file")
            logger.info("4. Ensure ports 8000, 8001, 8002, 8080 are not in use")
            logger.info("5. Wait a few minutes for services to fully initialize")
        
        logger.info("\n🔗 Access URLs:")
        logger.info(f"  🕷️ Gradio Interface: {self.gradio_url}")
        logger.info(f"  🔧 FastAPI Docs: {self.base_url}/docs")
        logger.info(f"  📊 NocoDB: {self.nocodb_url}")
        logger.info(f"  🔍 Crawl4AI Service: {self.crawl4ai_url}")
        
        return results


async def main():
    """Main test function"""
    tester = InstallationTester()
    results = await tester.run_all_tests()
    
    # Save results to file
    with open("test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"\n💾 Test results saved to test_results.json")
    
    # Exit with appropriate code
    exit_code = 0 if results["overall_success"] else 1
    exit(exit_code)


if __name__ == "__main__":
    asyncio.run(main())
