#!/usr/bin/env python3
"""
Performance Monitoring Script for Multi-Agent Crawler Orchestrator
Optimized for 4 vCPU, 16GB RAM systems
"""

import asyncio
import json
import time
import psutil
import docker
from datetime import datetime
from typing import Dict, List, Any

import httpx
from loguru import logger


class PerformanceMonitor:
    """Monitor system and application performance"""
    
    def __init__(self):
        self.docker_client = docker.from_env()
        self.services = {
            "crawler_orchestrator": "http://localhost:8000",
            "crawl4ai_service": "http://localhost:8002",
            "nocodb": "http://localhost:8080",
            "tablet_interface": "http://localhost:8004",
            "streamlit_control": "http://localhost:8003",
            "gradio_interface": "http://localhost:8001"
        }
        
    def get_system_stats(self) -> Dict[str, Any]:
        """Get system resource statistics"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "percent": cpu_percent,
                "count": psutil.cpu_count(),
                "load_avg": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            },
            "memory": {
                "total_gb": round(memory.total / 1024**3, 2),
                "available_gb": round(memory.available / 1024**3, 2),
                "used_gb": round(memory.used / 1024**3, 2),
                "percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / 1024**3, 2),
                "free_gb": round(disk.free / 1024**3, 2),
                "used_gb": round(disk.used / 1024**3, 2),
                "percent": round((disk.used / disk.total) * 100, 1)
            }
        }
    
    def get_docker_stats(self) -> Dict[str, Any]:
        """Get Docker container statistics"""
        containers = {}
        
        try:
            for container in self.docker_client.containers.list():
                stats = container.stats(stream=False)
                
                # Calculate CPU percentage
                cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                           stats['precpu_stats']['cpu_usage']['total_usage']
                system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                              stats['precpu_stats']['system_cpu_usage']
                
                cpu_percent = 0.0
                if system_delta > 0:
                    cpu_percent = (cpu_delta / system_delta) * \
                                 len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
                
                # Calculate memory usage
                memory_usage = stats['memory_stats']['usage']
                memory_limit = stats['memory_stats']['limit']
                memory_percent = (memory_usage / memory_limit) * 100
                
                containers[container.name] = {
                    "status": container.status,
                    "cpu_percent": round(cpu_percent, 2),
                    "memory_usage_mb": round(memory_usage / 1024**2, 2),
                    "memory_limit_mb": round(memory_limit / 1024**2, 2),
                    "memory_percent": round(memory_percent, 2),
                    "network_rx_mb": round(
                        stats['networks']['eth0']['rx_bytes'] / 1024**2, 2
                    ) if 'networks' in stats and 'eth0' in stats['networks'] else 0,
                    "network_tx_mb": round(
                        stats['networks']['eth0']['tx_bytes'] / 1024**2, 2
                    ) if 'networks' in stats and 'eth0' in stats['networks'] else 0
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting Docker stats: {e}")
        
        return containers
    
    async def check_service_health(self) -> Dict[str, Any]:
        """Check health of all services"""
        health_status = {}
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            for service_name, url in self.services.items():
                try:
                    start_time = time.time()
                    
                    if service_name == "nocodb":
                        # NocoDB doesn't have a health endpoint, just check if it responds
                        response = await client.get(url)
                        healthy = response.status_code == 200
                    else:
                        # Try health endpoint first, fallback to root
                        try:
                            response = await client.get(f"{url}/health")
                            healthy = response.status_code == 200
                        except:
                            response = await client.get(url)
                            healthy = response.status_code == 200
                    
                    response_time = time.time() - start_time
                    
                    health_status[service_name] = {
                        "healthy": healthy,
                        "response_time_ms": round(response_time * 1000, 2),
                        "status_code": response.status_code,
                        "url": url
                    }
                    
                except Exception as e:
                    health_status[service_name] = {
                        "healthy": False,
                        "error": str(e),
                        "url": url
                    }
        
        return health_status
    
    async def get_application_metrics(self) -> Dict[str, Any]:
        """Get application-specific metrics"""
        metrics = {}
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Get system status from main API
                response = await client.get("http://localhost:8000/api/v1/status")
                if response.status_code == 200:
                    metrics["system_status"] = response.json()
                
                # Get available models
                response = await client.get("http://localhost:8000/api/v1/models")
                if response.status_code == 200:
                    metrics["available_models"] = response.json()
                    
        except Exception as e:
            logger.error(f"❌ Error getting application metrics: {e}")
            metrics["error"] = str(e)
        
        return metrics
    
    def analyze_performance(self, stats: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze performance and provide recommendations"""
        analysis = {
            "overall_health": "good",
            "warnings": [],
            "recommendations": [],
            "resource_efficiency": {}
        }
        
        # Analyze system resources
        cpu_percent = stats["system"]["cpu"]["percent"]
        memory_percent = stats["system"]["memory"]["percent"]
        
        if cpu_percent > 80:
            analysis["warnings"].append(f"High CPU usage: {cpu_percent}%")
            analysis["recommendations"].append("Consider reducing concurrent operations")
            analysis["overall_health"] = "warning"
        
        if memory_percent > 85:
            analysis["warnings"].append(f"High memory usage: {memory_percent}%")
            analysis["recommendations"].append("Enable memory optimization features")
            analysis["overall_health"] = "warning"
        
        # Analyze Docker containers
        total_container_memory = 0
        unhealthy_containers = []
        
        for container_name, container_stats in stats["docker"].items():
            if container_stats["status"] != "running":
                unhealthy_containers.append(container_name)
            
            total_container_memory += container_stats["memory_usage_mb"]
            
            if container_stats["memory_percent"] > 90:
                analysis["warnings"].append(
                    f"Container {container_name} using {container_stats['memory_percent']:.1f}% of allocated memory"
                )
        
        if unhealthy_containers:
            analysis["warnings"].append(f"Unhealthy containers: {', '.join(unhealthy_containers)}")
            analysis["overall_health"] = "critical"
        
        # Analyze service health
        unhealthy_services = [
            name for name, health in stats["services"].items() 
            if not health.get("healthy", False)
        ]
        
        if unhealthy_services:
            analysis["warnings"].append(f"Unhealthy services: {', '.join(unhealthy_services)}")
            analysis["overall_health"] = "critical"
        
        # Resource efficiency
        analysis["resource_efficiency"] = {
            "cpu_efficiency": "good" if cpu_percent < 50 else "poor",
            "memory_efficiency": "good" if memory_percent < 70 else "poor",
            "container_memory_mb": round(total_container_memory, 2),
            "target_memory_usage": "< 2GB",
            "target_cpu_usage": "< 50%"
        }
        
        # Recommendations based on analysis
        if not analysis["warnings"]:
            analysis["recommendations"].append("System is performing optimally")
        
        if cpu_percent > 50:
            analysis["recommendations"].append("Consider reducing MAX_CONCURRENT_CRAWLS")
        
        if memory_percent > 70:
            analysis["recommendations"].append("Enable streaming for large datasets")
            analysis["recommendations"].append("Increase cache cleanup frequency")
        
        return analysis
    
    async def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        logger.info("📊 Generating performance report...")
        
        # Collect all metrics
        system_stats = self.get_system_stats()
        docker_stats = self.get_docker_stats()
        service_health = await self.check_service_health()
        app_metrics = await self.get_application_metrics()
        
        # Compile report
        report = {
            "report_timestamp": datetime.now().isoformat(),
            "system": system_stats,
            "docker": docker_stats,
            "services": service_health,
            "application": app_metrics
        }
        
        # Analyze performance
        analysis = self.analyze_performance(report)
        report["analysis"] = analysis
        
        return report
    
    def print_summary(self, report: Dict[str, Any]):
        """Print a human-readable summary"""
        print("\n" + "="*60)
        print("🕷️ MULTI-AGENT CRAWLER PERFORMANCE REPORT")
        print("="*60)
        
        # System overview
        system = report["system"]
        print(f"\n🖥️ SYSTEM RESOURCES:")
        print(f"   CPU Usage: {system['cpu']['percent']:.1f}%")
        print(f"   Memory: {system['memory']['used_gb']:.1f}GB / {system['memory']['total_gb']:.1f}GB ({system['memory']['percent']:.1f}%)")
        print(f"   Disk: {system['disk']['used_gb']:.1f}GB / {system['disk']['total_gb']:.1f}GB ({system['disk']['percent']:.1f}%)")
        
        # Service health
        print(f"\n🔧 SERVICE HEALTH:")
        for service, health in report["services"].items():
            status = "✅" if health.get("healthy", False) else "❌"
            response_time = health.get("response_time_ms", "N/A")
            print(f"   {status} {service}: {response_time}ms")
        
        # Docker containers
        print(f"\n🐳 DOCKER CONTAINERS:")
        for container, stats in report["docker"].items():
            status_icon = "✅" if stats["status"] == "running" else "❌"
            print(f"   {status_icon} {container}: CPU {stats['cpu_percent']:.1f}%, Memory {stats['memory_usage_mb']:.0f}MB")
        
        # Analysis
        analysis = report["analysis"]
        health_icon = {"good": "✅", "warning": "⚠️", "critical": "❌"}[analysis["overall_health"]]
        print(f"\n📊 PERFORMANCE ANALYSIS:")
        print(f"   Overall Health: {health_icon} {analysis['overall_health'].upper()}")
        
        if analysis["warnings"]:
            print(f"   ⚠️ Warnings:")
            for warning in analysis["warnings"]:
                print(f"      • {warning}")
        
        if analysis["recommendations"]:
            print(f"   💡 Recommendations:")
            for rec in analysis["recommendations"]:
                print(f"      • {rec}")
        
        print("\n" + "="*60)


async def main():
    """Main monitoring function"""
    monitor = PerformanceMonitor()
    
    try:
        report = await monitor.generate_report()
        
        # Print summary to console
        monitor.print_summary(report)
        
        # Save detailed report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"performance_report_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n💾 Detailed report saved to: {filename}")
        
        # Return exit code based on health
        health = report["analysis"]["overall_health"]
        if health == "critical":
            exit(2)
        elif health == "warning":
            exit(1)
        else:
            exit(0)
            
    except Exception as e:
        logger.error(f"❌ Monitoring failed: {e}")
        exit(3)


if __name__ == "__main__":
    asyncio.run(main())
