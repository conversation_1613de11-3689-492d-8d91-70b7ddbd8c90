#!/bin/bash

# Multi-Agent Crawler Package Update Script
# Updates all services with latest package versions

set -e

echo "🚀 Multi-Agent Crawler Package Update Script"
echo "=============================================="
echo "This script will update all Python packages to their latest versions"
echo "as specified in the updated requirements.txt files."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the multi-agent-crawler directory."
    exit 1
fi

# Function to update packages in a service
update_service_packages() {
    local service_name=$1
    local service_dir=$2
    
    print_status "Updating packages for $service_name..."
    
    if [ ! -d "$service_dir" ]; then
        print_warning "Directory $service_dir not found, skipping $service_name"
        return
    fi
    
    if [ ! -f "$service_dir/requirements.txt" ]; then
        print_warning "requirements.txt not found in $service_dir, skipping $service_name"
        return
    fi
    
    cd "$service_dir"
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        print_status "Creating virtual environment for $service_name..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip first
    print_status "Upgrading pip..."
    pip install --upgrade pip
    
    # Install updated requirements
    print_status "Installing updated requirements..."
    pip install -r requirements.txt
    
    # Deactivate virtual environment
    deactivate
    
    cd ..
    print_success "$service_name packages updated successfully!"
}

# Function to rebuild Docker images
rebuild_docker_images() {
    print_status "Rebuilding Docker images with updated packages..."
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker-compose down
    
    # Remove old images to force rebuild
    print_status "Removing old images..."
    docker-compose build --no-cache
    
    print_success "Docker images rebuilt successfully!"
}

# Function to verify updates
verify_updates() {
    print_status "Verifying package updates..."
    
    if [ -f "verify_updates.py" ]; then
        python3 verify_updates.py
    else
        print_warning "verify_updates.py not found, skipping verification"
    fi
}

# Main update process
main() {
    print_status "Starting package update process..."
    echo ""
    
    # Update each service
    update_service_packages "Crawler Orchestrator" "crawler-orchestrator"
    echo ""
    
    update_service_packages "Crawl4AI Service" "crawl4ai-service"
    echo ""
    
    update_service_packages "Scheduler" "scheduler"
    echo ""
    
    # Ask user if they want to rebuild Docker images
    echo ""
    read -p "Do you want to rebuild Docker images with updated packages? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rebuild_docker_images
        echo ""
    else
        print_warning "Skipping Docker image rebuild. You can rebuild later with: docker-compose build --no-cache"
        echo ""
    fi
    
    # Verify updates
    verify_updates
    
    echo ""
    print_success "Package update process completed!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Test your application to ensure compatibility"
    echo "2. If you skipped Docker rebuild, run: docker-compose build --no-cache"
    echo "3. Start services with: docker-compose up -d"
    echo "4. Check logs with: docker-compose logs -f"
    echo ""
    echo "📚 For detailed changes, see CHANGELOG.md"
}

# Check for help flag
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Multi-Agent Crawler Package Update Script"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --docker-only  Only rebuild Docker images (skip local updates)"
    echo "  --verify-only  Only run verification (skip updates)"
    echo ""
    echo "This script updates all Python packages in the multi-agent-crawler"
    echo "project to their latest versions as specified in requirements.txt files."
    exit 0
fi

# Handle special flags
if [ "$1" = "--docker-only" ]; then
    rebuild_docker_images
    exit 0
fi

if [ "$1" = "--verify-only" ]; then
    verify_updates
    exit 0
fi

# Run main process
main
