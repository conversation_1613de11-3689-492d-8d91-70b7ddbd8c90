version: '3.8'

services:
  # NocoDB - Database and API
  nocodb:
    image: nocodb/nocodb:latest
    container_name: nocodb
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - NC_DB=sqlite3://data/noco.db
      - NC_AUTH_JWT_SECRET=your-jwt-secret-here
      - NC_PUBLIC_URL=http://localhost:8080
      - NC_DISABLE_TELE=true
    volumes:
      - nocodb_data:/usr/app/data
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    container_name: redis_crawler
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --maxmemory 1gb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10
    volumes:
      - redis_data:/data
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Multi-Agent Crawler Orchestrator
  crawler-orchestrator:
    build:
      context: ./crawler-orchestrator
      dockerfile: Dockerfile
    container_name: crawler_orchestrator
    restart: unless-stopped
    ports:
      - "8000:8000"
      - "8001:8001"  # Gradio interface
      - "8003:8003"  # Streamlit control panel
      - "8004:8004"  # Tablet interface
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - EXA_API_KEY=${EXA_API_KEY}
      - BRAVE_API_KEY=${BRAVE_API_KEY}
      - NOCODB_URL=http://nocodb:8080
      - REDIS_URL=redis://redis:6379
      - PYTHONPATH=/app
      - MAX_CONCURRENT_CRAWLS=3
      - CRAWL_TIMEOUT=180
      - CACHE_TTL=7200
      - DEFAULT_MODEL=google/gemma-2-9b-it
      - FALLBACK_MODEL=google/gemma-2-27b-it
    volumes:
      - ./crawler-orchestrator:/app
      - crawler_data:/app/data
      - ./shared:/app/shared
    depends_on:
      - nocodb
      - redis
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Crawl4AI Service
  crawl4ai:
    build:
      context: ./crawl4ai-service
      dockerfile: Dockerfile
    container_name: crawl4ai_service
    restart: unless-stopped
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379
      - MAX_CONCURRENT_CRAWLS=3
      - CRAWL_TIMEOUT=180
    volumes:
      - crawl4ai_data:/app/data
    depends_on:
      - redis
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Task Scheduler
  scheduler:
    build:
      context: ./scheduler
      dockerfile: Dockerfile
    container_name: task_scheduler
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis:6379
      - NOCODB_URL=http://nocodb:8080
    volumes:
      - ./scheduler:/app
    depends_on:
      - redis
      - nocodb
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

volumes:
  nocodb_data:
  redis_data:
  crawler_data:
  crawl4ai_data:

networks:
  crawler_network:
    driver: bridge
