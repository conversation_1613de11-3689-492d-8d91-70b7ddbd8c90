#!/usr/bin/env python3
"""
Crawl4AI Service
Dedicated service for web crawling operations
"""

import asyncio
import os
from typing import Dict, List, Optional, Any
from datetime import datetime

import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from loguru import logger
from crawl4ai import AsyncWebCrawler

app = FastAPI(
    title="Crawl4AI Service",
    description="Dedicated web crawling service using Crawl4AI",
    version="1.0.0"
)

# Global crawler instance
crawler = None


class CrawlRequest(BaseModel):
    url: str
    word_count_threshold: int = 10
    bypass_cache: bool = False
    include_raw_html: bool = False


class CrawlResponse(BaseModel):
    success: bool
    url: str
    title: Optional[str] = None
    markdown: Optional[str] = None
    cleaned_html: Optional[str] = None
    raw_html: Optional[str] = None
    links: Optional[Dict] = None
    media: Optional[Dict] = None
    metadata: Optional[Dict] = None
    word_count: int = 0
    error: Optional[str] = None
    crawled_at: str


async def initialize_crawler():
    """Initialize the crawler"""
    global crawler
    try:
        crawler = AsyncWebCrawler(
            verbose=True,
            headless=True,
            browser_type="chromium"
        )
        await crawler.start()
        logger.info("✅ Crawl4AI service initialized")
    except Exception as e:
        logger.error(f"❌ Failed to initialize crawler: {e}")
        raise


async def cleanup_crawler():
    """Cleanup crawler resources"""
    global crawler
    if crawler:
        await crawler.close()
        logger.info("✅ Crawler cleaned up")


@app.on_event("startup")
async def startup_event():
    """Application startup"""
    await initialize_crawler()


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown"""
    await cleanup_crawler()


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Crawl4AI Service",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    global crawler
    if crawler:
        return {"status": "healthy", "crawler": "ready"}
    else:
        return {"status": "unhealthy", "crawler": "not_ready"}


@app.post("/crawl", response_model=CrawlResponse)
async def crawl_url(request: CrawlRequest):
    """Crawl a single URL"""
    global crawler
    
    if not crawler:
        raise HTTPException(status_code=503, detail="Crawler not initialized")
    
    try:
        logger.info(f"🕷️ Crawling URL: {request.url}")
        
        result = await crawler.arun(
            url=request.url,
            word_count_threshold=request.word_count_threshold,
            bypass_cache=request.bypass_cache,
            include_raw_html=request.include_raw_html
        )
        
        if result.success:
            response = CrawlResponse(
                success=True,
                url=request.url,
                title=result.metadata.get("title", ""),
                markdown=result.markdown,
                cleaned_html=result.cleaned_html,
                links=result.links,
                media=result.media,
                metadata=result.metadata,
                word_count=len(result.markdown.split()) if result.markdown else 0,
                crawled_at=datetime.now().isoformat()
            )
            
            if request.include_raw_html:
                response.raw_html = result.html
            
            logger.info(f"✅ Successfully crawled: {request.url}")
            return response
        else:
            logger.error(f"❌ Failed to crawl {request.url}: {result.error_message}")
            return CrawlResponse(
                success=False,
                url=request.url,
                error=result.error_message,
                crawled_at=datetime.now().isoformat()
            )
            
    except Exception as e:
        logger.error(f"❌ Error crawling {request.url}: {e}")
        return CrawlResponse(
            success=False,
            url=request.url,
            error=str(e),
            crawled_at=datetime.now().isoformat()
        )


@app.post("/crawl/batch")
async def crawl_batch(urls: List[str], **kwargs):
    """Crawl multiple URLs"""
    if not urls:
        raise HTTPException(status_code=400, detail="No URLs provided")
    
    logger.info(f"🕷️ Batch crawling {len(urls)} URLs")
    
    tasks = []
    for url in urls:
        request = CrawlRequest(url=url, **kwargs)
        tasks.append(crawl_url(request))
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Convert exceptions to error responses
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append(CrawlResponse(
                success=False,
                url=urls[i],
                error=str(result),
                crawled_at=datetime.now().isoformat()
            ))
        else:
            processed_results.append(result)
    
    successful_crawls = sum(1 for r in processed_results if r.success)
    logger.info(f"✅ Batch crawling completed: {successful_crawls}/{len(urls)} successful")
    
    return {
        "total_urls": len(urls),
        "successful_crawls": successful_crawls,
        "results": processed_results
    }


@app.get("/status")
async def get_status():
    """Get service status"""
    global crawler
    
    return {
        "service": "Crawl4AI Service",
        "status": "running",
        "crawler_ready": bool(crawler),
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info"
    )
