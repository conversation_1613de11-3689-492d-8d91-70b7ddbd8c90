#!/usr/bin/env python3
"""
Task Scheduler Service
Handles background tasks and scheduled operations
"""

import asyncio
import os
import time
from datetime import datetime, timedelta

import schedule
import redis
from loguru import logger


class TaskScheduler:
    """Task scheduler for background operations"""
    
    def __init__(self):
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.nocodb_url = os.getenv("NOCODB_URL", "http://localhost:8080")
        self.redis_client = None
        
    async def initialize(self):
        """Initialize the scheduler"""
        try:
            self.redis_client = redis.from_url(self.redis_url, decode_responses=True)
            
            # Test Redis connection
            self.redis_client.ping()
            logger.info("✅ Task Scheduler initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize scheduler: {e}")
            raise
    
    def cleanup_old_cache(self):
        """Clean up old cache entries"""
        try:
            logger.info("🧹 Cleaning up old cache entries...")
            
            # Get all cache keys
            cache_keys = self.redis_client.keys("search:*") + self.redis_client.keys("crawl:*")
            
            cleaned_count = 0
            for key in cache_keys:
                # Check if key is older than 24 hours
                ttl = self.redis_client.ttl(key)
                if ttl == -1:  # No expiration set
                    self.redis_client.expire(key, 86400)  # Set 24 hour expiration
                    cleaned_count += 1
            
            logger.info(f"✅ Cache cleanup completed: {cleaned_count} keys processed")
            
        except Exception as e:
            logger.error(f"❌ Cache cleanup error: {e}")
    
    def process_background_tasks(self):
        """Process background tasks from queue"""
        try:
            logger.info("🔄 Processing background tasks...")
            
            # Process research mission queue
            while True:
                task = self.redis_client.brpop("queue:research", timeout=1)
                if not task:
                    break
                
                _, task_data = task
                logger.info(f"📋 Processing research task: {task_data}")
                
                # Here you would process the actual task
                # For now, just log it
                
            # Process crawling queue
            while True:
                task = self.redis_client.brpop("queue:crawling", timeout=1)
                if not task:
                    break
                
                _, task_data = task
                logger.info(f"🕷️ Processing crawling task: {task_data}")
                
                # Here you would process the actual task
                # For now, just log it
            
            logger.info("✅ Background task processing completed")
            
        except Exception as e:
            logger.error(f"❌ Background task processing error: {e}")
    
    def update_system_stats(self):
        """Update system statistics"""
        try:
            logger.info("📊 Updating system statistics...")
            
            # Get Redis stats
            redis_info = self.redis_client.info()
            
            # Store stats in Redis
            stats = {
                "timestamp": datetime.now().isoformat(),
                "redis_memory": redis_info.get("used_memory_human", "0B"),
                "redis_clients": redis_info.get("connected_clients", 0),
                "redis_commands": redis_info.get("total_commands_processed", 0)
            }
            
            self.redis_client.setex("system:stats", 3600, str(stats))
            
            logger.info("✅ System statistics updated")
            
        except Exception as e:
            logger.error(f"❌ System stats update error: {e}")
    
    def health_check(self):
        """Perform health check on services"""
        try:
            logger.info("🏥 Performing health check...")
            
            # Check Redis
            redis_healthy = True
            try:
                self.redis_client.ping()
            except:
                redis_healthy = False
            
            # Store health status
            health_status = {
                "timestamp": datetime.now().isoformat(),
                "redis": redis_healthy,
                "scheduler": True
            }
            
            self.redis_client.setex("system:health", 300, str(health_status))
            
            logger.info(f"✅ Health check completed: Redis={redis_healthy}")
            
        except Exception as e:
            logger.error(f"❌ Health check error: {e}")
    
    def setup_schedules(self):
        """Setup scheduled tasks"""
        logger.info("⏰ Setting up scheduled tasks...")
        
        # Cache cleanup - every 6 hours
        schedule.every(6).hours.do(self.cleanup_old_cache)
        
        # Background task processing - every 5 minutes
        schedule.every(5).minutes.do(self.process_background_tasks)
        
        # System stats update - every 15 minutes
        schedule.every(15).minutes.do(self.update_system_stats)
        
        # Health check - every 2 minutes
        schedule.every(2).minutes.do(self.health_check)
        
        logger.info("✅ Scheduled tasks configured")
    
    def run(self):
        """Run the scheduler"""
        logger.info("🚀 Starting Task Scheduler...")
        
        # Setup schedules
        self.setup_schedules()
        
        # Run initial tasks
        self.health_check()
        self.update_system_stats()
        
        logger.info("✅ Task Scheduler running")
        
        # Main loop
        while True:
            try:
                schedule.run_pending()
                time.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                logger.info("🛑 Scheduler stopped by user")
                break
            except Exception as e:
                logger.error(f"❌ Scheduler error: {e}")
                time.sleep(60)  # Wait a minute before retrying


async def main():
    """Main function"""
    scheduler = TaskScheduler()
    await scheduler.initialize()
    scheduler.run()


if __name__ == "__main__":
    asyncio.run(main())
