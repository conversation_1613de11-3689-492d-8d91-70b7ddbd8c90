# OpenRouter API Key for LLM access
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Exa API Key for semantic search
EXA_API_KEY=

# Brave Search API Key
BRAVE_API_KEY=your_brave_api_key_here

# NocoDB Configuration
NOCODB_JWT_SECRET=your-super-secret-jwt-key-here
NOCODB_PUBLIC_URL=http://localhost:8080

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Crawler Configuration (Optimized for 4 vCPU, 16GB RAM)
MAX_CONCURRENT_CRAWLS=3
CRAWL_TIMEOUT=180
USER_AGENT=MultiAgentCrawler/1.0

# LLM Configuration (Cost Optimized)
DEFAULT_MODEL=google/gemma-2-9b-it
FALLBACK_MODEL=google/gemma-2-27b-it
TEMPERATURE=0.7
MAX_TOKENS=2000

# Connection Optimization
MAX_CONNECTIONS=10
CONNECTION_TIMEOUT=30
REQUEST_DELAY=0.5
BATCH_SIZE=5

# Memory Optimization
STREAMING_THRESHOLD=1000000
MAX_MEMORY_USAGE=0.8

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/crawler.log

# Data Storage (Enhanced Caching)
DATA_DIR=./data
CACHE_TTL=7200
CACHE_MAX_SIZE=1000
