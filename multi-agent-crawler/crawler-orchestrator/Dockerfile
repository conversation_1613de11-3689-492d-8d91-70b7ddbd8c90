FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Playwright browsers
RUN pip install playwright==1.52.0
RUN playwright install chromium
RUN playwright install-deps chromium

# Set working directory
WORKDIR /app

# Upgrade pip to handle dependency issues better
RUN pip install --upgrade pip

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt || \
    (echo "Error installing requirements. Checking for problematic packages..." && \
     grep -E '\(|\)' requirements.txt && \
     exit 1)

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/data /app/logs /app/shared

# Expose ports
EXPOSE 8000 8001 8003

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Start command
CMD ["python", "main.py"]
