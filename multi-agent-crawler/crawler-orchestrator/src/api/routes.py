"""
FastAPI routes for the Multi-Agent Crawler Orchestrator
"""

from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from loguru import logger

from ..agents.crew_orchestrator import crew_orchestrator
from ..services.search_service import search_service
from ..services.crawler_service import crawler_service
from ..services.llm_service import llm_service


router = APIRouter()


# Request/Response Models
class SearchRequest(BaseModel):
    query: str
    num_results: int = 10
    use_exa: bool = True
    use_brave: bool = True


class CrawlRequest(BaseModel):
    urls: List[str]
    extract_content: bool = True
    include_links: bool = False


class ResearchMissionRequest(BaseModel):
    topic: str
    objectives: List[str]
    constraints: Optional[Dict[str, Any]] = None


class ContentAnalysisRequest(BaseModel):
    content: str
    analysis_type: str = "general"


class ApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    timestamp: str = datetime.now().isoformat()


# Search Endpoints
@router.post("/search", response_model=ApiResponse)
async def web_search(request: SearchRequest):
    """Perform web search using multiple providers"""
    try:
        results = await search_service.combined_search(
            query=request.query,
            num_results=request.num_results,
            use_exa=request.use_exa,
            use_brave=request.use_brave
        )
        
        return ApiResponse(
            success=True,
            data={
                "query": request.query,
                "results": results,
                "count": len(results)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Search API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/semantic", response_model=ApiResponse)
async def semantic_search(request: SearchRequest):
    """Perform semantic search using Exa"""
    try:
        results = await search_service.semantic_search(
            query=request.query,
            num_results=request.num_results
        )
        
        return ApiResponse(
            success=True,
            data={
                "query": request.query,
                "results": results,
                "count": len(results)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Semantic search API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Crawling Endpoints
@router.post("/crawl", response_model=ApiResponse)
async def crawl_urls(request: CrawlRequest):
    """Crawl multiple URLs"""
    try:
        if not request.urls:
            raise HTTPException(status_code=400, detail="No URLs provided")
        
        results = await crawler_service.crawl_multiple_urls(request.urls)
        
        successful_crawls = sum(1 for r in results if r.get("success", False))
        
        return ApiResponse(
            success=True,
            data={
                "urls": request.urls,
                "results": results,
                "successful_crawls": successful_crawls,
                "total_urls": len(request.urls)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Crawl API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/crawl/single")
async def crawl_single_url(
    url: str,
    extract_content: bool = True,
    include_raw_html: bool = False
):
    """Crawl a single URL"""
    try:
        result = await crawler_service.crawl_url(
            url=url,
            include_raw_html=include_raw_html
        )
        
        return ApiResponse(
            success=result.get("success", False),
            data=result
        )
        
    except Exception as e:
        logger.error(f"❌ Single crawl API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Analysis Endpoints
@router.post("/analyze/content", response_model=ApiResponse)
async def analyze_content(request: ContentAnalysisRequest):
    """Analyze content using LLM"""
    try:
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="No content provided")
        
        analysis = await llm_service.analyze_content(
            content=request.content,
            analysis_type=request.analysis_type
        )
        
        return ApiResponse(
            success=True,
            data={
                "analysis_type": request.analysis_type,
                "analysis": analysis,
                "content_length": len(request.content)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Content analysis API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze/entities")
async def extract_entities(content: str):
    """Extract named entities from text"""
    try:
        if not content.strip():
            raise HTTPException(status_code=400, detail="No content provided")
        
        entities = await llm_service.extract_entities(content)
        
        return ApiResponse(
            success=True,
            data={
                "entities": entities,
                "count": len(entities)
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Entity extraction API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Research Mission Endpoints
@router.post("/research/mission", response_model=ApiResponse)
async def execute_research_mission(request: ResearchMissionRequest):
    """Execute a research mission using AI agents"""
    try:
        if not request.topic.strip():
            raise HTTPException(status_code=400, detail="No topic provided")
        
        if not request.objectives:
            raise HTTPException(status_code=400, detail="No objectives provided")
        
        result = await crew_orchestrator.execute_research_mission(
            topic=request.topic,
            objectives=request.objectives,
            constraints=request.constraints
        )
        
        return ApiResponse(
            success=result.get("success", False),
            data=result
        )
        
    except Exception as e:
        logger.error(f"❌ Research mission API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/research/targeted-crawl")
async def execute_targeted_crawl(
    urls: List[str],
    extraction_rules: Optional[Dict[str, Any]] = None
):
    """Execute a targeted crawling operation"""
    try:
        if not urls:
            raise HTTPException(status_code=400, detail="No URLs provided")
        
        result = await crew_orchestrator.execute_targeted_crawl(
            urls=urls,
            extraction_rules=extraction_rules
        )
        
        return ApiResponse(
            success=result.get("success", False),
            data=result
        )
        
    except Exception as e:
        logger.error(f"❌ Targeted crawl API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# System Endpoints
@router.get("/status")
async def get_system_status():
    """Get system status and health"""
    try:
        # Check service health
        llm_healthy = await llm_service.health_check()
        crawler_healthy = await crawler_service.health_check()
        
        # Get agent status
        agent_status = crew_orchestrator.get_agent_status()
        
        return ApiResponse(
            success=True,
            data={
                "services": {
                    "llm": llm_healthy,
                    "crawler": crawler_healthy,
                    "search": True,  # Assume healthy if no errors
                    "orchestrator": agent_status["initialized"]
                },
                "agents": agent_status["agents"],
                "tools": agent_status["tools"],
                "configuration": {
                    "default_model": llm_service.default_model,
                    "max_concurrent_crawls": crawler_service.max_concurrent,
                    "crawl_timeout": crawler_service.timeout
                }
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Status API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def get_available_models():
    """Get list of available LLM models"""
    try:
        models = llm_service.get_available_models()
        
        return ApiResponse(
            success=True,
            data={
                "models": models,
                "current_model": llm_service.default_model
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Models API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Background task endpoints
@router.post("/tasks/research")
async def start_background_research(
    background_tasks: BackgroundTasks,
    request: ResearchMissionRequest
):
    """Start a research mission as a background task"""
    try:
        task_id = f"research_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        background_tasks.add_task(
            _background_research_task,
            task_id,
            request.topic,
            request.objectives,
            request.constraints
        )
        
        return ApiResponse(
            success=True,
            data={
                "task_id": task_id,
                "status": "started",
                "message": "Research mission started in background"
            }
        )
        
    except Exception as e:
        logger.error(f"❌ Background research API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _background_research_task(
    task_id: str,
    topic: str,
    objectives: List[str],
    constraints: Optional[Dict[str, Any]]
):
    """Background task for research missions"""
    try:
        logger.info(f"🚀 Starting background research task: {task_id}")
        
        result = await crew_orchestrator.execute_research_mission(
            topic=topic,
            objectives=objectives,
            constraints=constraints
        )
        
        # Here you could save results to database or file system
        logger.info(f"✅ Background research task completed: {task_id}")
        
    except Exception as e:
        logger.error(f"❌ Background research task failed: {task_id} - {e}")
