"""
Custom tools for CrewAI agents
"""

import asyncio
import json
from typing import Dict, List, Optional, Any

from crewai_tools import BaseTool
from pydantic import BaseModel, Field
from loguru import logger

from ..services.search_service import search_service
from ..services.crawler_service import crawler_service
from ..services.llm_service import llm_service


class WebSearchInput(BaseModel):
    """Input schema for web search tool"""
    query: str = Field(..., description="Search query")
    num_results: int = Field(default=10, description="Number of results to return")
    use_exa: bool = Field(default=True, description="Use Exa search")
    use_brave: bool = Field(default=True, description="Use Brave search")


class WebSearchTool(BaseTool):
    """Tool for web searching using multiple providers"""
    
    name: str = "web_search"
    description: str = "Search the web using multiple search engines (Exa and Brave)"
    args_schema: type[BaseModel] = WebSearchInput
    
    async def initialize(self):
        """Initialize the search service"""
        await search_service.initialize()
    
    def _run(self, query: str, num_results: int = 10, use_exa: bool = True, use_brave: bool = True) -> str:
        """Execute web search"""
        try:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(
                search_service.combined_search(
                    query=query,
                    num_results=num_results,
                    use_exa=use_exa,
                    use_brave=use_brave
                )
            )
            
            loop.close()
            
            if not results:
                return "No search results found."
            
            # Format results for agent consumption
            formatted_results = []
            for i, result in enumerate(results[:num_results], 1):
                formatted_result = f"""
Result {i}:
Title: {result.get('title', 'N/A')}
URL: {result.get('url', 'N/A')}
Description: {result.get('description', result.get('text', 'N/A'))[:200]}...
Source: {result.get('source', 'N/A')}
"""
                formatted_results.append(formatted_result)
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"❌ Web search tool error: {e}")
            return f"Error performing web search: {str(e)}"


class WebCrawlerInput(BaseModel):
    """Input schema for web crawler tool"""
    urls: List[str] = Field(..., description="List of URLs to crawl")
    extract_content: bool = Field(default=True, description="Extract main content")
    include_links: bool = Field(default=False, description="Include extracted links")


class WebCrawlerTool(BaseTool):
    """Tool for web crawling using Crawl4AI"""
    
    name: str = "web_crawler"
    description: str = "Crawl web pages and extract content"
    args_schema: type[BaseModel] = WebCrawlerInput
    
    async def initialize(self):
        """Initialize the crawler service"""
        await crawler_service.initialize()
    
    def _run(self, urls: List[str], extract_content: bool = True, include_links: bool = False) -> str:
        """Execute web crawling"""
        try:
            if not urls:
                return "No URLs provided for crawling."
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(
                crawler_service.crawl_multiple_urls(urls)
            )
            
            loop.close()
            
            if not results:
                return "No crawling results obtained."
            
            # Format results for agent consumption
            formatted_results = []
            for i, result in enumerate(results, 1):
                if result.get("success"):
                    content = f"""
Crawl Result {i}:
URL: {result.get('url', 'N/A')}
Title: {result.get('title', 'N/A')}
Word Count: {result.get('word_count', 0)}
"""
                    
                    if extract_content and result.get('markdown'):
                        content += f"Content Preview: {result['markdown'][:500]}...\n"
                    
                    if include_links and result.get('links'):
                        internal_links = result['links'].get('internal', [])[:5]
                        if internal_links:
                            content += f"Internal Links: {', '.join([link.get('href', '') for link in internal_links])}\n"
                    
                    formatted_results.append(content)
                else:
                    formatted_results.append(f"""
Crawl Result {i}:
URL: {result.get('url', 'N/A')}
Status: Failed
Error: {result.get('error', 'Unknown error')}
""")
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"❌ Web crawler tool error: {e}")
            return f"Error crawling websites: {str(e)}"


class ContentAnalysisInput(BaseModel):
    """Input schema for content analysis tool"""
    content: str = Field(..., description="Content to analyze")
    analysis_type: str = Field(default="general", description="Type of analysis to perform")


class ContentAnalysisTool(BaseTool):
    """Tool for content analysis using LLM"""
    
    name: str = "content_analysis"
    description: str = "Analyze content and extract insights using AI"
    args_schema: type[BaseModel] = ContentAnalysisInput
    
    async def initialize(self):
        """Initialize the LLM service"""
        await llm_service.initialize()
    
    def _run(self, content: str, analysis_type: str = "general") -> str:
        """Execute content analysis"""
        try:
            if not content.strip():
                return "No content provided for analysis."
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            analysis = loop.run_until_complete(
                llm_service.analyze_content(content, analysis_type)
            )
            
            loop.close()
            
            if isinstance(analysis, dict):
                # Format structured analysis
                formatted_analysis = f"""
Content Analysis Results:

Summary: {analysis.get('summary', 'N/A')}

Key Points:
{chr(10).join(f"- {point}" for point in analysis.get('key_points', []))}

Sentiment: {analysis.get('sentiment', 'N/A')}

Main Topics:
{chr(10).join(f"- {topic}" for topic in analysis.get('topics', []))}

Important Entities:
{chr(10).join(f"- {entity}" for entity in analysis.get('entities', []))}

Insights: {analysis.get('insights', 'N/A')}
"""
                return formatted_analysis
            else:
                return f"Content Analysis: {analysis}"
            
        except Exception as e:
            logger.error(f"❌ Content analysis tool error: {e}")
            return f"Error analyzing content: {str(e)}"


class DataCuratorInput(BaseModel):
    """Input schema for data curator tool"""
    data: str = Field(..., description="Data to organize and clean")
    operation: str = Field(default="organize", description="Operation to perform: organize, deduplicate, categorize")


class DataCuratorTool(BaseTool):
    """Tool for data organization and curation"""
    
    name: str = "data_curator"
    description: str = "Organize, clean, and structure data"
    args_schema: type[BaseModel] = DataCuratorInput
    
    def _run(self, data: str, operation: str = "organize") -> str:
        """Execute data curation"""
        try:
            if not data.strip():
                return "No data provided for curation."
            
            if operation == "organize":
                return self._organize_data(data)
            elif operation == "deduplicate":
                return self._deduplicate_data(data)
            elif operation == "categorize":
                return self._categorize_data(data)
            else:
                return f"Unknown operation: {operation}"
            
        except Exception as e:
            logger.error(f"❌ Data curator tool error: {e}")
            return f"Error curating data: {str(e)}"
    
    def _organize_data(self, data: str) -> str:
        """Organize data into structured format"""
        lines = data.split('\n')
        organized_sections = {
            "urls": [],
            "titles": [],
            "content": [],
            "metadata": []
        }
        
        current_section = "content"
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.startswith("http"):
                organized_sections["urls"].append(line)
            elif line.startswith("Title:"):
                organized_sections["titles"].append(line[6:].strip())
            elif any(keyword in line.lower() for keyword in ["result", "source", "date"]):
                organized_sections["metadata"].append(line)
            else:
                organized_sections["content"].append(line)
        
        result = "Organized Data:\n\n"
        for section, items in organized_sections.items():
            if items:
                result += f"{section.upper()}:\n"
                for item in items[:10]:  # Limit to first 10 items
                    result += f"- {item}\n"
                result += "\n"
        
        return result
    
    def _deduplicate_data(self, data: str) -> str:
        """Remove duplicate entries from data"""
        lines = data.split('\n')
        seen = set()
        unique_lines = []
        
        for line in lines:
            line = line.strip()
            if line and line not in seen:
                seen.add(line)
                unique_lines.append(line)
        
        return f"Deduplicated Data ({len(unique_lines)} unique items):\n\n" + '\n'.join(unique_lines)
    
    def _categorize_data(self, data: str) -> str:
        """Categorize data by type and relevance"""
        lines = data.split('\n')
        categories = {
            "high_relevance": [],
            "medium_relevance": [],
            "low_relevance": [],
            "metadata": []
        }
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Simple categorization based on keywords
            if any(keyword in line.lower() for keyword in ["important", "key", "main", "primary"]):
                categories["high_relevance"].append(line)
            elif any(keyword in line.lower() for keyword in ["result", "source", "url", "title"]):
                categories["metadata"].append(line)
            elif any(keyword in line.lower() for keyword in ["secondary", "additional", "also"]):
                categories["medium_relevance"].append(line)
            else:
                categories["low_relevance"].append(line)
        
        result = "Categorized Data:\n\n"
        for category, items in categories.items():
            if items:
                result += f"{category.replace('_', ' ').upper()}:\n"
                for item in items[:5]:  # Limit to first 5 items per category
                    result += f"- {item}\n"
                result += "\n"
        
        return result


class SemanticSearchInput(BaseModel):
    """Input schema for semantic search tool"""
    query: str = Field(..., description="Semantic search query")
    num_results: int = Field(default=10, description="Number of results to return")


class SemanticSearchTool(BaseTool):
    """Tool for semantic search using Exa"""

    name: str = "semantic_search"
    description: str = "Perform semantic search to find contextually relevant content"
    args_schema: type[BaseModel] = SemanticSearchInput

    async def initialize(self):
        """Initialize the search service"""
        await search_service.initialize()

    def _run(self, query: str, num_results: int = 10) -> str:
        """Execute semantic search"""
        try:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            results = loop.run_until_complete(
                search_service.semantic_search(
                    query=query,
                    num_results=num_results
                )
            )

            loop.close()

            if not results:
                return "No semantic search results found."

            # Format results for agent consumption
            formatted_results = []
            for i, result in enumerate(results[:num_results], 1):
                formatted_result = f"""
Semantic Result {i}:
Title: {result.get('title', 'N/A')}
URL: {result.get('url', 'N/A')}
Relevance Score: {result.get('score', 'N/A')}
Content Preview: {result.get('text', 'N/A')[:300]}...
"""
                formatted_results.append(formatted_result)

            return "\n".join(formatted_results)

        except Exception as e:
            logger.error(f"❌ Semantic search tool error: {e}")
            return f"Error performing semantic search: {str(e)}"
