from crewai_tools import tool
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
import requests
from bs4 import BeautifulSoup
import json

# Tool for property research
@tool("Property Research Tool")
def property_research_tool(location: str, min_price: int, max_price: int) -> str:
    """Research premium real estate properties in a given location and price range."""
    # In a real implementation, this would connect to MLS or premium APIs
    # For demo purposes, we'll simulate with sample data
    sample_data = [
        {
            "address": "1 Luxury Lane, Beverly Hills",
            "price": "$12,500,000",
            "features": ["6 bedrooms", "8 bathrooms", "Ocean view", "Home theater"],
            "description": "Stunning contemporary masterpiece with panoramic views"
        },
        {
            "address": "100 Prestige Drive, Malibu",
            "price": "$8,900,000",
            "features": ["Beachfront", "Infinity pool", "Smart home", "Wine cellar"],
            "description": "Exclusive beachfront property with private access"
        }
    ]
    return json.dumps(sample_data)

# Tool for creating presentations
@tool("Presentation Creator Tool")
def presentation_creator_tool(property_data: str, company_name: str = "Cream Estates") -> str:
    """Create a luxurious real estate presentation for a property."""
    data = json.loads(property_data)
    prs = Presentation()
    
    # Title slide
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = f"Exclusive Property Presentation"
    subtitle.text = f"Presented by {company_name}\nPremium Real Estate"
    
    # Property details slide
    for prop in data:
        slide = prs.slides.add_slide(prs.slide_layouts[1])
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = prop["address"]
        content.text = (
            f"Price: {prop['price']}\n"
            f"Features: {', '.join(prop['features'])}\n\n"
            f"{prop['description']}"
        )
    
    # Save presentation
    filename = "luxury_property_presentation.pptx"
    prs.save(filename)
    return f"Presentation created successfully: {filename}"
