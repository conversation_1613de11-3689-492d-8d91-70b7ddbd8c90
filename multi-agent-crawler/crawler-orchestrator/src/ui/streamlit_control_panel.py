"""
Streamlit Control Panel for CrewAI Agents
Advanced interface for managing and monitoring multi-agent operations
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Import our services
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.crew_orchestrator import crew_orchestrator
from services.llm_service import llm_service
from services.search_service import search_service
from services.crawler_service import crawler_service
from services.redis_service import redis_service
from config import settings, LLM_MODELS, AGENT_ROLES


class StreamlitControlPanel:
    """Streamlit-based control panel for CrewAI agents"""
    
    def __init__(self):
        self.setup_page_config()
        self.initialize_session_state()
    
    def setup_page_config(self):
        """Configure Streamlit page"""
        st.set_page_config(
            page_title="🕷️ CrewAI Agent Control Panel",
            page_icon="🕷️",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .agent-card {
            background-color: #f0f2f6;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .metric-card {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_session_state(self):
        """Initialize Streamlit session state"""
        if 'services_initialized' not in st.session_state:
            st.session_state.services_initialized = False
        
        if 'active_missions' not in st.session_state:
            st.session_state.active_missions = {}
        
        if 'mission_history' not in st.session_state:
            st.session_state.mission_history = []
        
        if 'agent_performance' not in st.session_state:
            st.session_state.agent_performance = {
                'researcher': {'tasks_completed': 0, 'success_rate': 0.0},
                'analyzer': {'tasks_completed': 0, 'success_rate': 0.0},
                'curator': {'tasks_completed': 0, 'success_rate': 0.0},
                'reporter': {'tasks_completed': 0, 'success_rate': 0.0}
            }
    
    async def initialize_services(self):
        """Initialize all services"""
        if not st.session_state.services_initialized:
            with st.spinner("🔄 Initializing services..."):
                try:
                    await search_service.initialize()
                    await crawler_service.initialize()
                    await llm_service.initialize()
                    await crew_orchestrator.initialize()
                    await redis_service.connect()
                    
                    st.session_state.services_initialized = True
                    st.success("✅ All services initialized successfully!")
                except Exception as e:
                    st.error(f"❌ Service initialization failed: {e}")
                    return False
        return True
    
    def render_header(self):
        """Render the main header"""
        st.markdown('<h1 class="main-header">🕷️ CrewAI Agent Control Panel</h1>', unsafe_allow_html=True)
        st.markdown("---")
    
    def render_sidebar(self):
        """Render the sidebar with navigation and controls"""
        st.sidebar.title("🎛️ Control Panel")
        
        # Navigation
        page = st.sidebar.selectbox(
            "📍 Navigate",
            ["🏠 Dashboard", "🤖 Agent Management", "🎯 Mission Control", "📊 Analytics", "⚙️ Settings"]
        )
        
        st.sidebar.markdown("---")
        
        # Quick Actions
        st.sidebar.subheader("⚡ Quick Actions")
        
        if st.sidebar.button("🔄 Refresh Status"):
            st.rerun()
        
        if st.sidebar.button("🧹 Clear Cache"):
            self.clear_cache()
        
        if st.sidebar.button("📊 Generate Report"):
            self.generate_system_report()
        
        st.sidebar.markdown("---")
        
        # System Status
        st.sidebar.subheader("🔧 System Status")
        self.render_system_status_sidebar()
        
        return page
    
    def render_system_status_sidebar(self):
        """Render system status in sidebar"""
        # Service status indicators
        services = {
            "LLM Service": True,  # Placeholder - would check actual status
            "Search Service": True,
            "Crawler Service": True,
            "Redis Cache": True
        }
        
        for service, status in services.items():
            status_class = "status-healthy" if status else "status-error"
            st.sidebar.markdown(
                f'<span class="status-indicator {status_class}"></span>{service}',
                unsafe_allow_html=True
            )
    
    def render_dashboard(self):
        """Render the main dashboard"""
        st.header("🏠 Dashboard Overview")
        
        # Metrics row
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("🤖 Active Agents", "4", "0")
        
        with col2:
            st.metric("🎯 Active Missions", len(st.session_state.active_missions), "0")
        
        with col3:
            st.metric("📊 Completed Tasks", "127", "+12")
        
        with col4:
            st.metric("⚡ Success Rate", "94.2%", "+2.1%")
        
        st.markdown("---")
        
        # Agent Status Cards
        st.subheader("🤖 Agent Status Overview")
        
        col1, col2 = st.columns(2)
        
        with col1:
            self.render_agent_card("Research Agent", "researcher", "🔍", "Active", "Searching for Polish senior care facilities")
            self.render_agent_card("Content Analyzer", "analyzer", "📊", "Idle", "Ready for content analysis")
        
        with col2:
            self.render_agent_card("Data Curator", "curator", "🗂️", "Active", "Processing facility data")
            self.render_agent_card("Report Generator", "reporter", "📝", "Idle", "Ready to generate reports")
        
        # Recent Activity
        st.markdown("---")
        st.subheader("📈 Recent Activity")
        
        # Activity timeline
        activity_data = [
            {"time": "2 min ago", "agent": "Research Agent", "action": "Completed search for 'domy seniora Warszawa'", "status": "✅"},
            {"time": "5 min ago", "agent": "Data Curator", "action": "Processed 25 facility records", "status": "✅"},
            {"time": "8 min ago", "agent": "Content Analyzer", "action": "Analyzed website content for quality scoring", "status": "✅"},
            {"time": "12 min ago", "agent": "Research Agent", "action": "Started new search mission", "status": "🔄"},
        ]
        
        for activity in activity_data:
            st.markdown(f"**{activity['time']}** - {activity['status']} **{activity['agent']}**: {activity['action']}")
    
    def render_agent_card(self, name: str, agent_id: str, icon: str, status: str, description: str):
        """Render an agent status card"""
        status_color = "#28a745" if status == "Active" else "#6c757d"
        
        st.markdown(f"""
        <div class="agent-card">
            <h4>{icon} {name}</h4>
            <p><strong>Status:</strong> <span style="color: {status_color};">{status}</span></p>
            <p><strong>Current Task:</strong> {description}</p>
            <p><strong>Performance:</strong> {st.session_state.agent_performance[agent_id]['tasks_completed']} tasks completed</p>
        </div>
        """, unsafe_allow_html=True)
    
    def render_agent_management(self):
        """Render agent management interface"""
        st.header("🤖 Agent Management")
        
        # Agent configuration
        st.subheader("⚙️ Agent Configuration")
        
        selected_agent = st.selectbox(
            "Select Agent to Configure",
            ["Research Agent", "Content Analyzer", "Data Curator", "Report Generator"]
        )
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🎯 Agent Settings")
            
            # Model selection
            model = st.selectbox(
                "LLM Model",
                list(LLM_MODELS.keys()),
                help="Select the language model for this agent"
            )
            
            # Temperature setting
            temperature = st.slider(
                "Temperature",
                min_value=0.0,
                max_value=1.0,
                value=0.7,
                step=0.1,
                help="Controls randomness in agent responses"
            )
            
            # Max tokens
            max_tokens = st.number_input(
                "Max Tokens",
                min_value=100,
                max_value=8000,
                value=2000,
                help="Maximum tokens per response"
            )
            
            # Tools selection
            st.subheader("🛠️ Available Tools")
            tools = st.multiselect(
                "Select Tools",
                ["Web Search", "Web Crawler", "Content Analysis", "Data Curator", "Semantic Search"],
                default=["Web Search", "Web Crawler"]
            )
        
        with col2:
            st.subheader("📊 Agent Performance")
            
            # Performance metrics
            agent_key = selected_agent.lower().split()[0]
            if agent_key in st.session_state.agent_performance:
                perf = st.session_state.agent_performance[agent_key]
                
                st.metric("Tasks Completed", perf['tasks_completed'])
                st.metric("Success Rate", f"{perf['success_rate']:.1%}")
                
                # Performance chart
                fig = go.Figure(data=go.Scatter(
                    x=list(range(24)),
                    y=[85, 87, 92, 88, 94, 91, 96, 93, 89, 95, 92, 88, 90, 94, 96, 93, 91, 89, 92, 95, 93, 91, 94, 92],
                    mode='lines+markers',
                    name='Success Rate %'
                ))
                fig.update_layout(
                    title="24-Hour Performance Trend",
                    xaxis_title="Hour",
                    yaxis_title="Success Rate (%)",
                    height=300
                )
                st.plotly_chart(fig, use_container_width=True)
        
        # Agent Actions
        st.markdown("---")
        st.subheader("🎮 Agent Actions")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("▶️ Start Agent"):
                st.success(f"✅ {selected_agent} started")
        
        with col2:
            if st.button("⏸️ Pause Agent"):
                st.warning(f"⏸️ {selected_agent} paused")
        
        with col3:
            if st.button("🔄 Restart Agent"):
                st.info(f"🔄 {selected_agent} restarted")
        
        with col4:
            if st.button("🛑 Stop Agent"):
                st.error(f"🛑 {selected_agent} stopped")
    
    def render_mission_control(self):
        """Render mission control interface"""
        st.header("🎯 Mission Control Center")
        
        # Mission creation
        st.subheader("🚀 Create New Mission")
        
        with st.form("mission_form"):
            mission_name = st.text_input("Mission Name", placeholder="Polish Senior Care Discovery")
            
            topic = st.text_area(
                "Research Topic",
                placeholder="Domy seniora w Polsce - comprehensive facility discovery",
                height=100
            )
            
            objectives = st.text_area(
                "Mission Objectives (one per line)",
                placeholder="""Znajdź wszystkie działające domy seniora w Polsce
Wyciągnij pełne dane kontaktowe (adres, telefon, email, strona)
Zweryfikuj jakość i aktualność informacji
Stwórz bazę danych z analizą rynku""",
                height=150
            )
            
            col1, col2 = st.columns(2)
            
            with col1:
                max_sources = st.slider("Max Sources", 10, 100, 50)
                geographic_scope = st.selectbox("Geographic Scope", ["Poland", "Europe", "Global"])
            
            with col2:
                language = st.selectbox("Language", ["Polish", "English", "Multi-language"])
                quality_threshold = st.slider("Quality Threshold", 0.5, 1.0, 0.8)
            
            submitted = st.form_submit_button("🚀 Launch Mission")
            
            if submitted and topic and objectives:
                mission_id = f"mission_{int(time.time())}"
                objectives_list = [obj.strip() for obj in objectives.split('\n') if obj.strip()]
                
                mission_data = {
                    "id": mission_id,
                    "name": mission_name,
                    "topic": topic,
                    "objectives": objectives_list,
                    "constraints": {
                        "max_sources": max_sources,
                        "geographic_scope": geographic_scope,
                        "language": language,
                        "quality_threshold": quality_threshold
                    },
                    "status": "active",
                    "created_at": datetime.now().isoformat(),
                    "progress": 0
                }
                
                st.session_state.active_missions[mission_id] = mission_data
                st.success(f"✅ Mission '{mission_name}' launched successfully!")
                st.rerun()
        
        # Active missions
        st.markdown("---")
        st.subheader("🔄 Active Missions")
        
        if st.session_state.active_missions:
            for mission_id, mission in st.session_state.active_missions.items():
                with st.expander(f"🎯 {mission['name']} - {mission['status'].upper()}"):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write(f"**Topic:** {mission['topic']}")
                        st.write(f"**Created:** {mission['created_at']}")
                        st.write(f"**Status:** {mission['status']}")
                        
                        # Progress bar
                        progress = mission.get('progress', 0)
                        st.progress(progress / 100)
                        st.write(f"Progress: {progress}%")
                    
                    with col2:
                        st.write("**Objectives:**")
                        for i, obj in enumerate(mission['objectives'], 1):
                            st.write(f"{i}. {obj}")
                        
                        # Mission actions
                        col_a, col_b, col_c = st.columns(3)
                        with col_a:
                            if st.button(f"⏸️ Pause", key=f"pause_{mission_id}"):
                                mission['status'] = 'paused'
                                st.rerun()
                        with col_b:
                            if st.button(f"📊 Report", key=f"report_{mission_id}"):
                                st.info("Generating mission report...")
                        with col_c:
                            if st.button(f"🛑 Stop", key=f"stop_{mission_id}"):
                                mission['status'] = 'stopped'
                                st.rerun()
        else:
            st.info("No active missions. Create a new mission above.")
    
    def render_analytics(self):
        """Render analytics dashboard"""
        st.header("📊 Analytics Dashboard")
        
        # Performance metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.subheader("🎯 Mission Success Rate")
            fig = go.Figure(data=go.Pie(
                labels=['Successful', 'Failed', 'In Progress'],
                values=[75, 15, 10],
                hole=0.4
            ))
            fig.update_layout(height=300)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            st.subheader("🤖 Agent Performance")
            agents = ['Research', 'Analyzer', 'Curator', 'Reporter']
            performance = [94, 87, 92, 89]
            
            fig = go.Figure(data=go.Bar(x=agents, y=performance))
            fig.update_layout(
                yaxis_title="Success Rate (%)",
                height=300
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col3:
            st.subheader("📈 Daily Activity")
            dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
            activity = [45, 52, 48, 61, 55, 67, 59, 72, 68, 75, 71, 78, 82, 79, 85, 88, 92, 89, 94, 91, 96, 93, 98, 95, 102, 99, 105, 108, 112, 115]
            
            fig = go.Figure(data=go.Scatter(x=dates, y=activity, mode='lines+markers'))
            fig.update_layout(
                yaxis_title="Tasks Completed",
                height=300
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Detailed analytics
        st.markdown("---")
        st.subheader("📋 Detailed Performance Analysis")
        
        # Create sample data
        performance_data = pd.DataFrame({
            'Agent': ['Research Agent', 'Content Analyzer', 'Data Curator', 'Report Generator'] * 7,
            'Date': pd.date_range(start='2024-01-01', periods=28, freq='D'),
            'Tasks_Completed': [12, 8, 15, 5, 14, 9, 16, 6, 13, 10, 17, 7, 15, 11, 18, 8, 16, 12, 19, 9, 17, 13, 20, 10, 18, 14, 21, 11],
            'Success_Rate': [0.94, 0.87, 0.92, 0.89, 0.95, 0.88, 0.93, 0.90, 0.96, 0.89, 0.94, 0.91, 0.97, 0.90, 0.95, 0.92, 0.98, 0.91, 0.96, 0.93, 0.99, 0.92, 0.97, 0.94, 0.98, 0.93, 0.98, 0.95]
        })
        
        # Interactive chart
        fig = px.line(
            performance_data,
            x='Date',
            y='Success_Rate',
            color='Agent',
            title='Agent Performance Over Time'
        )
        st.plotly_chart(fig, use_container_width=True)
        
        # Data table
        st.subheader("📊 Performance Summary")
        summary_data = performance_data.groupby('Agent').agg({
            'Tasks_Completed': 'sum',
            'Success_Rate': 'mean'
        }).round(3)
        st.dataframe(summary_data, use_container_width=True)
    
    def render_settings(self):
        """Render settings interface"""
        st.header("⚙️ System Settings")
        
        # API Configuration
        st.subheader("🔑 API Configuration")
        
        col1, col2 = st.columns(2)
        
        with col1:
            openrouter_key = st.text_input(
                "OpenRouter API Key",
                value="sk-or-v1-***",
                type="password",
                help="API key for OpenRouter LLM access"
            )
            
            exa_key = st.text_input(
                "Exa API Key",
                value="exa_***",
                type="password",
                help="API key for Exa semantic search"
            )
        
        with col2:
            brave_key = st.text_input(
                "Brave Search API Key",
                value="BSA***",
                type="password",
                help="API key for Brave Search"
            )
            
            nocodb_url = st.text_input(
                "NocoDB URL",
                value="http://localhost:8080",
                help="URL for NocoDB database"
            )
        
        # System Configuration
        st.markdown("---")
        st.subheader("🔧 System Configuration")
        
        col1, col2 = st.columns(2)
        
        with col1:
            max_concurrent = st.number_input(
                "Max Concurrent Crawls",
                min_value=1,
                max_value=20,
                value=5,
                help="Maximum number of concurrent crawling operations"
            )
            
            crawl_timeout = st.number_input(
                "Crawl Timeout (seconds)",
                min_value=30,
                max_value=600,
                value=300,
                help="Timeout for individual crawl operations"
            )
        
        with col2:
            cache_ttl = st.number_input(
                "Cache TTL (seconds)",
                min_value=300,
                max_value=86400,
                value=3600,
                help="Time-to-live for cached results"
            )
            
            log_level = st.selectbox(
                "Log Level",
                ["DEBUG", "INFO", "WARNING", "ERROR"],
                index=1,
                help="System logging level"
            )
        
        # Save settings
        if st.button("💾 Save Settings"):
            st.success("✅ Settings saved successfully!")
    
    def clear_cache(self):
        """Clear system cache"""
        st.success("🧹 Cache cleared successfully!")
    
    def generate_system_report(self):
        """Generate system performance report"""
        st.success("📊 System report generated!")
    
    def run(self):
        """Main application runner"""
        # Initialize services
        if asyncio.run(self.initialize_services()):
            # Render header
            self.render_header()
            
            # Render sidebar and get selected page
            page = self.render_sidebar()
            
            # Render selected page
            if page == "🏠 Dashboard":
                self.render_dashboard()
            elif page == "🤖 Agent Management":
                self.render_agent_management()
            elif page == "🎯 Mission Control":
                self.render_mission_control()
            elif page == "📊 Analytics":
                self.render_analytics()
            elif page == "⚙️ Settings":
                self.render_settings()


def main():
    """Main function to run the Streamlit app"""
    control_panel = StreamlitControlPanel()
    control_panel.run()


if __name__ == "__main__":
    main()
