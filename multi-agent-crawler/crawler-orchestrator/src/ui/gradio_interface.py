"""
Gradio Interface for Multi-Agent Crawler Orchestrator
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

import gradio as gr
from loguru import logger

from ..agents.crew_orchestrator import crew_orchestrator
from ..services.search_service import search_service
from ..services.crawler_service import crawler_service
from ..services.llm_service import llm_service
from ..config import settings, LLM_MODELS


class GradioInterface:
    """Gradio interface for the crawler orchestrator"""
    
    def __init__(self):
        self.app = None
        
    async def initialize_services(self):
        """Initialize all services"""
        try:
            await search_service.initialize()
            await crawler_service.initialize()
            await llm_service.initialize()
            await crew_orchestrator.initialize()
            logger.info("✅ All services initialized for Gradio interface")
        except Exception as e:
            logger.error(f"❌ Failed to initialize services: {e}")
            raise
    
    def research_mission(
        self,
        topic: str,
        objectives: str,
        max_sources: int = 20,
        content_type: str = "all types",
        time_range: str = "any time"
    ) -> str:
        """Execute a research mission"""
        try:
            if not topic.strip():
                return "❌ Please provide a research topic."
            
            objectives_list = [obj.strip() for obj in objectives.split('\n') if obj.strip()]
            if not objectives_list:
                return "❌ Please provide at least one research objective."
            
            constraints = {
                "max_sources": max_sources,
                "content_type": content_type,
                "time_range": time_range
            }
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                crew_orchestrator.execute_research_mission(
                    topic=topic,
                    objectives=objectives_list,
                    constraints=constraints
                )
            )
            
            loop.close()
            
            if result.get("success"):
                return f"""
✅ Research Mission Completed!

Topic: {result['topic']}
Duration: {result['duration']:.2f} seconds

Results:
{result['result']}
"""
            else:
                return f"❌ Research mission failed: {result.get('error', 'Unknown error')}"
                
        except Exception as e:
            logger.error(f"❌ Research mission error: {e}")
            return f"❌ Error: {str(e)}"
    
    def web_search(
        self,
        query: str,
        num_results: int = 10,
        use_exa: bool = True,
        use_brave: bool = True
    ) -> str:
        """Perform web search"""
        try:
            if not query.strip():
                return "❌ Please provide a search query."
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(
                search_service.combined_search(
                    query=query,
                    num_results=num_results,
                    use_exa=use_exa,
                    use_brave=use_brave
                )
            )
            
            loop.close()
            
            if not results:
                return "❌ No search results found."
            
            formatted_results = []
            for i, result in enumerate(results, 1):
                formatted_result = f"""
{i}. **{result.get('title', 'N/A')}**
   URL: {result.get('url', 'N/A')}
   Source: {result.get('source', 'N/A')}
   Description: {result.get('description', result.get('text', 'N/A'))[:200]}...
"""
                formatted_results.append(formatted_result)
            
            return f"✅ Found {len(results)} results:\n\n" + "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"❌ Web search error: {e}")
            return f"❌ Error: {str(e)}"
    
    def crawl_urls(
        self,
        urls_text: str,
        include_content: bool = True,
        include_links: bool = False
    ) -> str:
        """Crawl multiple URLs"""
        try:
            urls = [url.strip() for url in urls_text.split('\n') if url.strip()]
            if not urls:
                return "❌ Please provide at least one URL."
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            results = loop.run_until_complete(
                crawler_service.crawl_multiple_urls(urls)
            )
            
            loop.close()
            
            if not results:
                return "❌ No crawling results obtained."
            
            formatted_results = []
            successful_crawls = 0
            
            for i, result in enumerate(results, 1):
                if result.get("success"):
                    successful_crawls += 1
                    content = f"""
{i}. ✅ **{result.get('title', 'N/A')}**
   URL: {result.get('url', 'N/A')}
   Word Count: {result.get('word_count', 0)}
"""
                    
                    if include_content and result.get('markdown'):
                        content += f"   Content Preview: {result['markdown'][:300]}...\n"
                    
                    if include_links and result.get('links'):
                        internal_links = result['links'].get('internal', [])[:3]
                        if internal_links:
                            content += f"   Links: {', '.join([link.get('href', '') for link in internal_links])}\n"
                    
                    formatted_results.append(content)
                else:
                    formatted_results.append(f"""
{i}. ❌ **Failed**
   URL: {result.get('url', 'N/A')}
   Error: {result.get('error', 'Unknown error')}
""")
            
            return f"✅ Crawling completed: {successful_crawls}/{len(results)} successful\n\n" + "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"❌ Crawling error: {e}")
            return f"❌ Error: {str(e)}"
    
    def analyze_content(
        self,
        content: str,
        analysis_type: str = "general"
    ) -> str:
        """Analyze content using LLM"""
        try:
            if not content.strip():
                return "❌ Please provide content to analyze."
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            analysis = loop.run_until_complete(
                llm_service.analyze_content(content, analysis_type)
            )
            
            loop.close()
            
            if isinstance(analysis, dict) and "error" not in analysis:
                return f"""
✅ Content Analysis Results:

**Summary:** {analysis.get('summary', 'N/A')}

**Sentiment:** {analysis.get('sentiment', 'N/A')}

**Key Points:**
{chr(10).join(f"• {point}" for point in analysis.get('key_points', []))}

**Main Topics:**
{chr(10).join(f"• {topic}" for topic in analysis.get('topics', []))}

**Important Entities:**
{chr(10).join(f"• {entity}" for entity in analysis.get('entities', []))}

**Insights:** {analysis.get('insights', 'N/A')}
"""
            else:
                error_msg = analysis.get('error', 'Unknown error') if isinstance(analysis, dict) else str(analysis)
                return f"❌ Analysis failed: {error_msg}"
                
        except Exception as e:
            logger.error(f"❌ Content analysis error: {e}")
            return f"❌ Error: {str(e)}"
    
    def get_system_status(self) -> str:
        """Get system status"""
        try:
            # Run async health checks
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            llm_healthy = loop.run_until_complete(llm_service.health_check())
            crawler_healthy = loop.run_until_complete(crawler_service.health_check())
            
            loop.close()
            
            agent_status = crew_orchestrator.get_agent_status()
            
            status = f"""
🔧 **System Status**

**Services:**
• LLM Service: {'✅ Healthy' if llm_healthy else '❌ Unhealthy'}
• Crawler Service: {'✅ Healthy' if crawler_healthy else '❌ Unhealthy'}
• Search Service: ✅ Ready
• CrewAI Orchestrator: {'✅ Ready' if agent_status['initialized'] else '❌ Not Ready'}

**Agents:**
{chr(10).join(f"• {name}: {info['role']}" for name, info in agent_status['agents'].items())}

**Available Tools:**
{chr(10).join(f"• {tool}" for tool in agent_status['tools'])}

**Configuration:**
• Default Model: {settings.DEFAULT_MODEL}
• Max Concurrent Crawls: {settings.MAX_CONCURRENT_CRAWLS}
• Crawl Timeout: {settings.CRAWL_TIMEOUT}s
"""
            return status
            
        except Exception as e:
            logger.error(f"❌ Status check error: {e}")
            return f"❌ Error getting system status: {str(e)}"


def create_gradio_interface() -> gr.Blocks:
    """Create and configure the Gradio interface"""
    
    interface = GradioInterface()
    
    # Initialize services in background
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(interface.initialize_services())
        loop.close()
    except Exception as e:
        logger.error(f"❌ Failed to initialize services for Gradio: {e}")
    
    with gr.Blocks(
        title="Multi-Agent Crawler Orchestrator",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        """
    ) as app:
        
        gr.Markdown("""
        # 🕷️ Multi-Agent Crawler Orchestrator
        
        Advanced web crawling and research platform powered by AI agents.
        """)
        
        with gr.Tabs():
            
            # Research Mission Tab
            with gr.Tab("🔍 Research Mission"):
                gr.Markdown("Execute comprehensive research missions using AI agents.")
                
                with gr.Row():
                    with gr.Column():
                        topic_input = gr.Textbox(
                            label="Research Topic",
                            placeholder="Enter the topic you want to research...",
                            lines=2
                        )
                        objectives_input = gr.Textbox(
                            label="Research Objectives",
                            placeholder="Enter objectives, one per line...",
                            lines=5
                        )
                        
                        with gr.Row():
                            max_sources = gr.Slider(
                                label="Max Sources",
                                minimum=5,
                                maximum=50,
                                value=20,
                                step=5
                            )
                            content_type = gr.Dropdown(
                                label="Content Type",
                                choices=["all types", "articles", "research papers", "news", "blogs"],
                                value="all types"
                            )
                        
                        research_btn = gr.Button("🚀 Start Research Mission", variant="primary")
                    
                    with gr.Column():
                        research_output = gr.Textbox(
                            label="Research Results",
                            lines=20,
                            max_lines=30
                        )
                
                research_btn.click(
                    interface.research_mission,
                    inputs=[topic_input, objectives_input, max_sources, content_type],
                    outputs=research_output
                )
            
            # Web Search Tab
            with gr.Tab("🔎 Web Search"):
                gr.Markdown("Search the web using multiple search engines.")
                
                with gr.Row():
                    with gr.Column():
                        search_query = gr.Textbox(
                            label="Search Query",
                            placeholder="Enter your search query...",
                            lines=2
                        )
                        
                        with gr.Row():
                            num_results = gr.Slider(
                                label="Number of Results",
                                minimum=5,
                                maximum=50,
                                value=10,
                                step=5
                            )
                            use_exa = gr.Checkbox(label="Use Exa Search", value=True)
                            use_brave = gr.Checkbox(label="Use Brave Search", value=True)
                        
                        search_btn = gr.Button("🔍 Search", variant="primary")
                    
                    with gr.Column():
                        search_output = gr.Textbox(
                            label="Search Results",
                            lines=15,
                            max_lines=25
                        )
                
                search_btn.click(
                    interface.web_search,
                    inputs=[search_query, num_results, use_exa, use_brave],
                    outputs=search_output
                )
            
            # Web Crawler Tab
            with gr.Tab("🕷️ Web Crawler"):
                gr.Markdown("Crawl websites and extract content.")
                
                with gr.Row():
                    with gr.Column():
                        urls_input = gr.Textbox(
                            label="URLs to Crawl",
                            placeholder="Enter URLs, one per line...",
                            lines=8
                        )
                        
                        with gr.Row():
                            include_content = gr.Checkbox(label="Include Content Preview", value=True)
                            include_links = gr.Checkbox(label="Include Links", value=False)
                        
                        crawl_btn = gr.Button("🕷️ Start Crawling", variant="primary")
                    
                    with gr.Column():
                        crawl_output = gr.Textbox(
                            label="Crawling Results",
                            lines=15,
                            max_lines=25
                        )
                
                crawl_btn.click(
                    interface.crawl_urls,
                    inputs=[urls_input, include_content, include_links],
                    outputs=crawl_output
                )
            
            # Content Analysis Tab
            with gr.Tab("📊 Content Analysis"):
                gr.Markdown("Analyze content using AI.")
                
                with gr.Row():
                    with gr.Column():
                        content_input = gr.Textbox(
                            label="Content to Analyze",
                            placeholder="Paste content here...",
                            lines=10
                        )
                        
                        analysis_type = gr.Dropdown(
                            label="Analysis Type",
                            choices=["general", "sentiment", "entities", "topics", "summary"],
                            value="general"
                        )
                        
                        analyze_btn = gr.Button("📊 Analyze Content", variant="primary")
                    
                    with gr.Column():
                        analysis_output = gr.Textbox(
                            label="Analysis Results",
                            lines=15,
                            max_lines=25
                        )
                
                analyze_btn.click(
                    interface.analyze_content,
                    inputs=[content_input, analysis_type],
                    outputs=analysis_output
                )
            
            # System Status Tab
            with gr.Tab("⚙️ System Status"):
                gr.Markdown("Monitor system health and configuration.")
                
                status_btn = gr.Button("🔄 Refresh Status", variant="secondary")
                status_output = gr.Textbox(
                    label="System Status",
                    lines=20,
                    max_lines=30
                )
                
                status_btn.click(
                    interface.get_system_status,
                    outputs=status_output
                )
                
                # Auto-refresh on load
                app.load(interface.get_system_status, outputs=status_output)
        
        gr.Markdown("""
        ---
        **Multi-Agent Crawler Orchestrator v1.0** | Powered by CrewAI, Crawl4AI, Exa, and Brave Search
        """)
    
    return app
