"""
Tablet-Optimized Mobile Interface
Responsive design for iPad/Android tablets with touch-friendly controls
"""

import asyncio
import json
import base64
from datetime import datetime
from typing import Dict, List, Optional, Any
from io import BytesIO

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Import our services
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.crew_orchestrator import crew_orchestrator
from services.llm_service import llm_service
from services.search_service import search_service
from services.crawler_service import crawler_service
from services.memory_optimizer import memory_optimizer
from config import settings


class TabletInterface:
    """Tablet-optimized interface for CrewAI agents"""
    
    def __init__(self):
        self.setup_tablet_config()
        self.initialize_session_state()
    
    def setup_tablet_config(self):
        """Configure Streamlit for tablet optimization"""
        st.set_page_config(
            page_title="🕷️ Mobile Crawler Control",
            page_icon="🕷️",
            layout="wide",
            initial_sidebar_state="collapsed",  # Hide sidebar on mobile
            menu_items={
                'Get Help': None,
                'Report a bug': None,
                'About': "Multi-Agent Crawler - Tablet Interface"
            }
        )
        
        # Mobile-first CSS
        st.markdown("""
        <style>
        /* Mobile-first responsive design */
        .main .block-container {
            padding-top: 1rem;
            padding-bottom: 1rem;
            padding-left: 1rem;
            padding-right: 1rem;
            max-width: 100%;
        }
        
        /* Large touch-friendly buttons */
        .stButton > button {
            height: 60px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 12px;
            border: 2px solid #1f77b4;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 100%;
            margin: 8px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        
        /* Touch-friendly inputs */
        .stTextInput > div > div > input,
        .stTextArea > div > div > textarea,
        .stSelectbox > div > div > select {
            font-size: 16px;
            padding: 12px;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
        }
        
        /* Card-style containers */
        .tablet-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }
        
        /* Status indicators */
        .status-active {
            background: linear-gradient(90deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 4px;
        }
        
        .status-idle {
            background: linear-gradient(90deg, #9E9E9E, #757575);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin: 4px;
        }
        
        /* Progress bars */
        .stProgress > div > div > div {
            height: 12px;
            border-radius: 6px;
        }
        
        /* Metrics */
        .metric-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 16px;
            text-align: center;
            margin: 8px 0;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 0;
        }
        
        .metric-label {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        /* Hide Streamlit elements */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        header {visibility: hidden;}
        
        /* Responsive charts */
        .js-plotly-plot {
            width: 100% !important;
        }
        
        /* Swipe indicators */
        .swipe-hint {
            text-align: center;
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }
        
        /* Touch feedback */
        .stButton > button:active {
            transform: scale(0.98);
        }
        
        /* Tablet-specific adjustments */
        @media (min-width: 768px) and (max-width: 1024px) {
            .main .block-container {
                padding-left: 2rem;
                padding-right: 2rem;
            }
            
            .metric-value {
                font-size: 3rem;
            }
        }
        
        /* Large tablet adjustments */
        @media (min-width: 1024px) {
            .main .block-container {
                max-width: 1200px;
                margin: 0 auto;
            }
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_session_state(self):
        """Initialize session state for tablet interface"""
        if 'tablet_view' not in st.session_state:
            st.session_state.tablet_view = 'dashboard'
        
        if 'active_missions' not in st.session_state:
            st.session_state.active_missions = {}
        
        if 'offline_data' not in st.session_state:
            st.session_state.offline_data = {}
        
        if 'last_sync' not in st.session_state:
            st.session_state.last_sync = datetime.now()
    
    def render_header(self):
        """Render mobile-optimized header"""
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col1:
            if st.button("📊", help="Dashboard", key="nav_dashboard"):
                st.session_state.tablet_view = 'dashboard'
                st.rerun()
        
        with col2:
            st.markdown("""
            <div style="text-align: center;">
                <h2 style="margin: 0; color: #1f77b4;">🕷️ Crawler Control</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            if st.button("⚙️", help="Settings", key="nav_settings"):
                st.session_state.tablet_view = 'settings'
                st.rerun()
    
    def render_navigation_tabs(self):
        """Render touch-friendly navigation tabs"""
        tabs = ["📊 Dashboard", "🎯 Missions", "🤖 Agents", "📈 Analytics", "💾 Data"]
        
        # Create horizontal scrollable tabs
        selected_tab = st.selectbox(
            "Navigate",
            tabs,
            index=0,
            label_visibility="collapsed"
        )
        
        # Map tab selection to view
        view_mapping = {
            "📊 Dashboard": "dashboard",
            "🎯 Missions": "missions", 
            "🤖 Agents": "agents",
            "📈 Analytics": "analytics",
            "💾 Data": "data"
        }
        
        st.session_state.tablet_view = view_mapping.get(selected_tab, "dashboard")
    
    def render_dashboard(self):
        """Render tablet-optimized dashboard"""
        st.markdown('<div class="tablet-card">', unsafe_allow_html=True)
        
        # Quick metrics in a grid
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("""
            <div class="metric-container">
                <p class="metric-value">4</p>
                <p class="metric-label">🤖 Active Agents</p>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown("""
            <div class="metric-container">
                <p class="metric-value">127</p>
                <p class="metric-label">📊 Tasks Done</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown("""
            <div class="metric-container">
                <p class="metric-value">2</p>
                <p class="metric-label">🎯 Active Missions</p>
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown("""
            <div class="metric-container">
                <p class="metric-value">94%</p>
                <p class="metric-label">⚡ Success Rate</p>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Agent status cards
        st.subheader("🤖 Agent Status")
        
        agents = [
            {"name": "Research Agent", "status": "Active", "task": "Searching Polish facilities"},
            {"name": "Content Analyzer", "status": "Idle", "task": "Ready for analysis"},
            {"name": "Data Curator", "status": "Active", "task": "Processing 25 records"},
            {"name": "Report Generator", "status": "Idle", "task": "Ready to generate reports"}
        ]
        
        for agent in agents:
            status_class = "status-active" if agent["status"] == "Active" else "status-idle"
            
            st.markdown(f"""
            <div class="tablet-card">
                <h4>🤖 {agent['name']}</h4>
                <span class="{status_class}">{agent['status']}</span>
                <p style="margin-top: 10px; color: #666;">{agent['task']}</p>
            </div>
            """, unsafe_allow_html=True)
    
    def render_mission_control(self):
        """Render tablet-optimized mission control"""
        st.subheader("🎯 Mission Control")
        
        # Quick mission launcher
        st.markdown('<div class="tablet-card">', unsafe_allow_html=True)
        st.markdown("### 🚀 Quick Mission")
        
        mission_type = st.selectbox(
            "Mission Type",
            ["Polish Senior Care Facilities", "Healthcare Providers", "Educational Institutions", "Custom Research"],
            key="quick_mission_type"
        )
        
        if mission_type == "Polish Senior Care Facilities":
            st.text_area(
                "Objectives",
                value="""Znajdź wszystkie działające domy seniora w Polsce
Wyciągnij pełne dane kontaktowe
Zweryfikuj jakość informacji
Stwórz bazę danych z analizą rynku""",
                height=120,
                key="quick_objectives"
            )
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🚀 Launch Mission", key="launch_quick_mission"):
                st.success("✅ Mission launched successfully!")
                st.balloons()
        
        with col2:
            if st.button("💾 Save Template", key="save_template"):
                st.info("📝 Template saved for later use")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Active missions
        st.markdown("### 🔄 Active Missions")
        
        if st.session_state.active_missions:
            for mission_id, mission in st.session_state.active_missions.items():
                progress = mission.get('progress', 0)
                
                st.markdown(f"""
                <div class="tablet-card">
                    <h4>🎯 {mission['name']}</h4>
                    <p><strong>Status:</strong> {mission['status']}</p>
                </div>
                """, unsafe_allow_html=True)
                
                st.progress(progress / 100)
                st.caption(f"Progress: {progress}%")
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button("⏸️ Pause", key=f"pause_{mission_id}"):
                        mission['status'] = 'paused'
                        st.rerun()
                
                with col2:
                    if st.button("📊 Report", key=f"report_{mission_id}"):
                        st.info("Generating report...")
                
                with col3:
                    if st.button("🛑 Stop", key=f"stop_{mission_id}"):
                        mission['status'] = 'stopped'
                        st.rerun()
        else:
            st.info("No active missions. Launch a new mission above!")
    
    def render_data_export(self):
        """Render tablet-optimized data export interface"""
        st.subheader("💾 Data Export")
        
        st.markdown('<div class="tablet-card">', unsafe_allow_html=True)
        
        # Export options
        export_format = st.selectbox(
            "Export Format",
            ["📱 Mobile HTML", "📊 Excel", "📄 PDF Report", "💾 JSON", "📋 CSV"],
            key="export_format"
        )
        
        data_type = st.selectbox(
            "Data Type",
            ["🏥 Senior Care Facilities", "📊 Mission Results", "🤖 Agent Performance", "📈 Analytics"],
            key="data_type"
        )
        
        # Preview data
        if st.button("👀 Preview Data", key="preview_data"):
            # Sample data for preview
            sample_data = pd.DataFrame({
                'Name': ['Dom Seniora "Słoneczny"', 'Centrum Opieki "Rodzinny Dom"', 'Villa Senior Care'],
                'City': ['Warszawa', 'Kraków', 'Gdańsk'],
                'Phone': ['+48 22 123 4567', '+48 12 987 6543', '+48 58 456 7890'],
                'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'Capacity': [50, 75, 30]
            })
            
            st.dataframe(sample_data, use_container_width=True)
        
        # Export buttons
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📱 Export for Tablet", key="export_tablet"):
                self.generate_tablet_export(export_format, data_type)
        
        with col2:
            if st.button("☁️ Save to Cloud", key="save_cloud"):
                st.info("💾 Data saved to cloud storage")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Offline data management
        st.markdown("### 📱 Offline Data")
        
        if st.button("💾 Download for Offline Use", key="download_offline"):
            self.prepare_offline_data()
            st.success("✅ Data prepared for offline use")
        
        if st.session_state.offline_data:
            st.info(f"📱 Offline data available: {len(st.session_state.offline_data)} records")
            
            if st.button("🔄 Sync with Server", key="sync_server"):
                st.session_state.last_sync = datetime.now()
                st.success("✅ Data synchronized")
    
    def generate_tablet_export(self, format_type: str, data_type: str):
        """Generate tablet-optimized export"""
        if "Mobile HTML" in format_type:
            html_content = self.create_mobile_html_report(data_type)
            
            # Create download link
            b64 = base64.b64encode(html_content.encode()).decode()
            href = f'<a href="data:text/html;base64,{b64}" download="mobile_report.html">📱 Download Mobile Report</a>'
            st.markdown(href, unsafe_allow_html=True)
            
        elif "PDF" in format_type:
            st.info("📄 PDF report generation started...")
            # PDF generation would be implemented here
            
        else:
            st.success(f"✅ {format_type} export completed!")
    
    def create_mobile_html_report(self, data_type: str) -> str:
        """Create mobile-optimized HTML report"""
        html_template = """
        <!DOCTYPE html>
        <html lang="pl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Mobile Crawler Report</title>
            <style>
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0; padding: 20px; background: #f5f5f5;
                }
                .container { max-width: 100%; background: white; border-radius: 12px; padding: 20px; }
                .header { text-align: center; color: #1f77b4; margin-bottom: 30px; }
                .facility { 
                    border: 1px solid #e0e0e0; border-radius: 8px; 
                    padding: 15px; margin: 15px 0; background: white;
                }
                .facility h3 { margin: 0 0 10px 0; color: #333; }
                .contact { color: #666; margin: 5px 0; }
                .phone, .email { 
                    display: inline-block; padding: 5px 10px; 
                    background: #e3f2fd; border-radius: 15px; margin: 5px 5px 5px 0;
                }
                @media (max-width: 768px) {
                    body { padding: 10px; }
                    .facility { padding: 12px; }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🕷️ Mobile Crawler Report</h1>
                    <p>Generated: {timestamp}</p>
                </div>
                
                <div class="facility">
                    <h3>🏥 Dom Seniora "Słoneczny"</h3>
                    <div class="contact">📍 ul. Słoneczna 15, 00-001 Warszawa</div>
                    <div class="phone">📞 +48 22 123 4567</div>
                    <div class="email">✉️ <EMAIL></div>
                    <div class="contact">🌐 www.sloneczny.pl</div>
                    <div class="contact">👥 Capacity: 50 beds</div>
                </div>
                
                <!-- More facilities would be added here -->
                
            </div>
        </body>
        </html>
        """.format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M"))
        
        return html_template
    
    def prepare_offline_data(self):
        """Prepare data for offline use"""
        # Sample offline data
        offline_data = {
            "facilities": [
                {
                    "name": "Dom Seniora Słoneczny",
                    "address": "ul. Słoneczna 15, Warszawa",
                    "phone": "+48 22 123 4567",
                    "email": "<EMAIL>",
                    "capacity": 50
                }
            ],
            "last_updated": datetime.now().isoformat(),
            "total_records": 1
        }
        
        st.session_state.offline_data = offline_data
    
    def run(self):
        """Main tablet interface runner"""
        self.render_header()
        self.render_navigation_tabs()
        
        # Render selected view
        if st.session_state.tablet_view == 'dashboard':
            self.render_dashboard()
        elif st.session_state.tablet_view == 'missions':
            self.render_mission_control()
        elif st.session_state.tablet_view == 'data':
            self.render_data_export()
        elif st.session_state.tablet_view == 'agents':
            st.subheader("🤖 Agent Management")
            st.info("Agent management interface - Coming soon!")
        elif st.session_state.tablet_view == 'analytics':
            st.subheader("📈 Analytics")
            st.info("Analytics dashboard - Coming soon!")
        else:
            self.render_dashboard()
        
        # Footer with sync status
        st.markdown("---")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.caption(f"🔄 Last sync: {st.session_state.last_sync.strftime('%H:%M')}")
        
        with col2:
            memory_stats = memory_optimizer.get_memory_stats()
            st.caption(f"💾 Memory: {memory_stats.memory_percent:.1f}%")
        
        with col3:
            st.caption("📱 Tablet Mode")


def main():
    """Main function to run the tablet interface"""
    tablet_interface = TabletInterface()
    tablet_interface.run()


if __name__ == "__main__":
    main()
