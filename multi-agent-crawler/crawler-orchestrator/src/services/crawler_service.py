"""
Crawler Service using Crawl4AI
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

import httpx
from loguru import logger
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy, JsonCssExtractionStrategy

from ..config import settings


class CrawlerService:
    """Service for web crawling using Crawl4AI"""
    
    def __init__(self):
        self.crawler = None
        self.max_concurrent = settings.MAX_CONCURRENT_CRAWLS
        self.timeout = settings.CRAWL_TIMEOUT
        self.user_agent = settings.USER_AGENT
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
        
    async def initialize(self):
        """Initialize the crawler"""
        try:
            self.crawler = AsyncWebCrawler(
                verbose=True,
                headless=True,
                browser_type="chromium"
            )
            await self.crawler.start()
            logger.info("✅ Crawler Service initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize crawler: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup crawler resources"""
        if self.crawler:
            await self.crawler.close()
            logger.info("✅ Crawler cleaned up")
    
    async def crawl_url(
        self,
        url: str,
        extraction_strategy: Optional[str] = None,
        css_selector: Optional[str] = None,
        word_count_threshold: int = 10,
        bypass_cache: bool = False,
        include_raw_html: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Crawl a single URL"""
        async with self.semaphore:
            try:
                if not self.crawler:
                    await self.initialize()
                
                crawl_params = {
                    "url": url,
                    "word_count_threshold": word_count_threshold,
                    "bypass_cache": bypass_cache,
                    "include_raw_html": include_raw_html,
                    **kwargs
                }
                
                # Add extraction strategy if specified
                if extraction_strategy == "llm" and css_selector:
                    crawl_params["extraction_strategy"] = LLMExtractionStrategy(
                        provider="openai",
                        api_token=settings.OPENROUTER_API_KEY,
                        instruction=f"Extract content from CSS selector: {css_selector}"
                    )
                elif extraction_strategy == "css" and css_selector:
                    crawl_params["extraction_strategy"] = JsonCssExtractionStrategy({
                        "content": css_selector
                    })
                
                result = await self.crawler.arun(**crawl_params)
                
                if result.success:
                    crawl_data = {
                        "url": url,
                        "title": result.metadata.get("title", ""),
                        "description": result.metadata.get("description", ""),
                        "keywords": result.metadata.get("keywords", ""),
                        "markdown": result.markdown,
                        "cleaned_html": result.cleaned_html,
                        "media": result.media,
                        "links": result.links,
                        "metadata": result.metadata,
                        "extracted_content": result.extracted_content,
                        "success": True,
                        "crawled_at": datetime.now().isoformat(),
                        "word_count": len(result.markdown.split()) if result.markdown else 0
                    }
                    
                    if include_raw_html:
                        crawl_data["raw_html"] = result.html
                    
                    logger.info(f"✅ Successfully crawled: {url}")
                    return crawl_data
                else:
                    logger.error(f"❌ Failed to crawl {url}: {result.error_message}")
                    return {
                        "url": url,
                        "success": False,
                        "error": result.error_message,
                        "crawled_at": datetime.now().isoformat()
                    }
                    
            except Exception as e:
                logger.error(f"❌ Error crawling {url}: {e}")
                return {
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "crawled_at": datetime.now().isoformat()
                }
    
    async def crawl_multiple_urls(
        self,
        urls: List[str],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl multiple URLs concurrently"""
        if not urls:
            return []
        
        logger.info(f"🕷️ Starting to crawl {len(urls)} URLs")
        
        tasks = [self.crawl_url(url, **kwargs) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        crawl_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Task failed for {urls[i]}: {result}")
                crawl_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": str(result),
                    "crawled_at": datetime.now().isoformat()
                })
            else:
                crawl_results.append(result)
        
        successful_crawls = sum(1 for r in crawl_results if r.get("success", False))
        logger.info(f"✅ Crawling completed: {successful_crawls}/{len(urls)} successful")
        
        return crawl_results
    
    async def extract_structured_data(
        self,
        url: str,
        schema: Dict[str, str],
        instruction: Optional[str] = None
    ) -> Dict[str, Any]:
        """Extract structured data using CSS selectors or LLM"""
        try:
            if instruction:
                # Use LLM extraction
                strategy = LLMExtractionStrategy(
                    provider="openai",
                    api_token=settings.OPENROUTER_API_KEY,
                    instruction=instruction
                )
            else:
                # Use CSS extraction
                strategy = JsonCssExtractionStrategy(schema)
            
            result = await self.crawl_url(
                url,
                extraction_strategy=strategy
            )
            
            if result.get("success") and result.get("extracted_content"):
                try:
                    extracted = json.loads(result["extracted_content"])
                    return {
                        "url": url,
                        "success": True,
                        "data": extracted,
                        "extracted_at": datetime.now().isoformat()
                    }
                except json.JSONDecodeError:
                    return {
                        "url": url,
                        "success": True,
                        "data": result["extracted_content"],
                        "extracted_at": datetime.now().isoformat()
                    }
            else:
                return {
                    "url": url,
                    "success": False,
                    "error": result.get("error", "Extraction failed"),
                    "extracted_at": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Error extracting structured data from {url}: {e}")
            return {
                "url": url,
                "success": False,
                "error": str(e),
                "extracted_at": datetime.now().isoformat()
            }
    
    async def crawl_with_pagination(
        self,
        base_url: str,
        max_pages: int = 10,
        pagination_selector: str = "a[rel='next']",
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl multiple pages with pagination"""
        results = []
        current_url = base_url
        page_count = 0
        
        while current_url and page_count < max_pages:
            logger.info(f"🕷️ Crawling page {page_count + 1}: {current_url}")
            
            result = await self.crawl_url(current_url, **kwargs)
            results.append(result)
            
            if not result.get("success"):
                break
            
            # Find next page URL
            next_url = None
            if result.get("links"):
                for link in result["links"]["internal"]:
                    if pagination_selector in str(link):
                        next_url = link.get("href")
                        break
            
            current_url = next_url
            page_count += 1
        
        logger.info(f"✅ Pagination crawling completed: {len(results)} pages")
        return results
    
    async def health_check(self) -> bool:
        """Check if the crawler service is healthy"""
        try:
            if not self.crawler:
                await self.initialize()
            
            # Test crawl a simple page
            result = await self.crawl_url("https://httpbin.org/html")
            return result.get("success", False)
        except Exception as e:
            logger.error(f"❌ Crawler health check failed: {e}")
            return False


# Global crawler service instance
crawler_service = CrawlerService()
