"""
Memory-Efficient Data Processing Service
Optimized for streaming and batch operations on limited resources
"""

import asyncio
import gc
import psutil
import json
from typing import AsyncGenerator, Dict, List, Any, Optional, Union
from dataclasses import dataclass
from io import StringIO
import csv

from loguru import logger

from ..config import settings


@dataclass
class MemoryStats:
    """Memory usage statistics"""
    total_memory: float
    available_memory: float
    used_memory: float
    memory_percent: float
    process_memory: float


class MemoryOptimizer:
    """Memory-efficient data processing service"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.memory_threshold = settings.MAX_MEMORY_USAGE
        self.streaming_threshold = settings.STREAMING_THRESHOLD
        
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory statistics"""
        memory = psutil.virtual_memory()
        process_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        return MemoryStats(
            total_memory=memory.total / 1024 / 1024 / 1024,  # GB
            available_memory=memory.available / 1024 / 1024 / 1024,  # GB
            used_memory=memory.used / 1024 / 1024 / 1024,  # GB
            memory_percent=memory.percent,
            process_memory=process_memory
        )
    
    def check_memory_usage(self) -> bool:
        """Check if memory usage is within acceptable limits"""
        stats = self.get_memory_stats()
        return stats.memory_percent < (self.memory_threshold * 100)
    
    async def force_garbage_collection(self):
        """Force garbage collection to free memory"""
        gc.collect()
        await asyncio.sleep(0.1)  # Allow GC to complete
        logger.debug("🗑️ Garbage collection completed")
    
    async def stream_large_data(
        self,
        data: Union[List[Dict], str],
        chunk_size: int = 1000
    ) -> AsyncGenerator[List[Dict], None]:
        """Stream large datasets in chunks to prevent memory overflow"""
        
        if isinstance(data, str):
            # Handle JSON string
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                logger.error("❌ Invalid JSON data for streaming")
                return
        
        if not isinstance(data, list):
            logger.error("❌ Data must be a list for streaming")
            return
        
        total_items = len(data)
        logger.info(f"📊 Streaming {total_items} items in chunks of {chunk_size}")
        
        for i in range(0, total_items, chunk_size):
            chunk = data[i:i + chunk_size]
            
            # Check memory usage before yielding chunk
            if not self.check_memory_usage():
                logger.warning("⚠️ High memory usage detected, forcing GC")
                await self.force_garbage_collection()
            
            yield chunk
            
            # Small delay to prevent overwhelming the system
            await asyncio.sleep(0.01)
    
    async def process_data_batch(
        self,
        data: List[Dict],
        processor_func,
        batch_size: Optional[int] = None
    ) -> List[Any]:
        """Process data in memory-efficient batches"""
        batch_size = batch_size or settings.BATCH_SIZE
        results = []
        
        logger.info(f"🔄 Processing {len(data)} items in batches of {batch_size}")
        
        async for chunk in self.stream_large_data(data, batch_size):
            try:
                # Process chunk
                if asyncio.iscoroutinefunction(processor_func):
                    chunk_results = await processor_func(chunk)
                else:
                    chunk_results = processor_func(chunk)
                
                results.extend(chunk_results)
                
                # Memory check after processing
                if not self.check_memory_usage():
                    await self.force_garbage_collection()
                
            except Exception as e:
                logger.error(f"❌ Batch processing error: {e}")
                continue
        
        return results
    
    async def stream_to_file(
        self,
        data: AsyncGenerator,
        file_path: str,
        format: str = "json"
    ):
        """Stream data directly to file to avoid memory accumulation"""
        
        logger.info(f"💾 Streaming data to {file_path} in {format} format")
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if format.lower() == "json":
                    f.write('[')
                    first_item = True
                    
                    async for chunk in data:
                        for item in chunk:
                            if not first_item:
                                f.write(',')
                            f.write(json.dumps(item, ensure_ascii=False))
                            first_item = False
                    
                    f.write(']')
                
                elif format.lower() == "csv":
                    writer = None
                    
                    async for chunk in data:
                        for item in chunk:
                            if writer is None:
                                # Initialize CSV writer with first item's keys
                                fieldnames = item.keys()
                                writer = csv.DictWriter(f, fieldnames=fieldnames)
                                writer.writeheader()
                            
                            writer.writerow(item)
                
                elif format.lower() == "jsonl":
                    async for chunk in data:
                        for item in chunk:
                            f.write(json.dumps(item, ensure_ascii=False) + '\n')
            
            logger.info(f"✅ Data successfully streamed to {file_path}")
            
        except Exception as e:
            logger.error(f"❌ Error streaming to file: {e}")
            raise
    
    def optimize_json_data(self, data: Dict) -> Dict:
        """Optimize JSON data structure to reduce memory usage"""
        
        def optimize_value(value):
            if isinstance(value, str):
                # Remove excessive whitespace
                return ' '.join(value.split())
            elif isinstance(value, list):
                # Remove duplicates while preserving order
                seen = set()
                return [optimize_value(x) for x in value 
                       if not (x in seen or seen.add(x))]
            elif isinstance(value, dict):
                # Recursively optimize nested dictionaries
                return {k: optimize_value(v) for k, v in value.items() 
                       if v is not None and v != ""}
            else:
                return value
        
        return optimize_value(data)
    
    async def compress_large_text(self, text: str, max_length: int = 10000) -> str:
        """Compress large text content while preserving important information"""
        
        if len(text) <= max_length:
            return text
        
        # Split into sentences
        sentences = text.split('. ')
        
        if len(sentences) <= 3:
            # If very few sentences, just truncate
            return text[:max_length] + "..."
        
        # Keep first and last sentences, summarize middle
        first_sentence = sentences[0]
        last_sentence = sentences[-1]
        middle_text = '. '.join(sentences[1:-1])
        
        # Calculate available space for middle content
        available_space = max_length - len(first_sentence) - len(last_sentence) - 20
        
        if len(middle_text) > available_space:
            # Truncate middle content
            middle_text = middle_text[:available_space] + "..."
        
        return f"{first_sentence}. {middle_text}. {last_sentence}"
    
    async def deduplicate_data(
        self,
        data: List[Dict],
        key_field: str = "url"
    ) -> List[Dict]:
        """Remove duplicate entries based on key field"""
        
        seen = set()
        unique_data = []
        
        for item in data:
            key_value = item.get(key_field)
            if key_value and key_value not in seen:
                seen.add(key_value)
                unique_data.append(item)
        
        removed_count = len(data) - len(unique_data)
        if removed_count > 0:
            logger.info(f"🔄 Removed {removed_count} duplicate entries")
        
        return unique_data
    
    def get_memory_usage_report(self) -> Dict[str, Any]:
        """Generate comprehensive memory usage report"""
        stats = self.get_memory_stats()
        
        return {
            "memory_stats": {
                "total_gb": round(stats.total_memory, 2),
                "available_gb": round(stats.available_memory, 2),
                "used_gb": round(stats.used_memory, 2),
                "usage_percent": round(stats.memory_percent, 1),
                "process_memory_mb": round(stats.process_memory, 1)
            },
            "thresholds": {
                "memory_threshold_percent": round(self.memory_threshold * 100, 1),
                "streaming_threshold_bytes": self.streaming_threshold,
                "within_limits": self.check_memory_usage()
            },
            "recommendations": self._get_memory_recommendations(stats)
        }
    
    def _get_memory_recommendations(self, stats: MemoryStats) -> List[str]:
        """Get memory optimization recommendations"""
        recommendations = []
        
        if stats.memory_percent > 85:
            recommendations.append("High memory usage detected - consider reducing concurrent operations")
        
        if stats.process_memory > 2000:  # 2GB
            recommendations.append("Process memory usage is high - consider restarting the service")
        
        if stats.available_memory < 2:  # Less than 2GB available
            recommendations.append("Low available memory - enable aggressive caching and streaming")
        
        if not recommendations:
            recommendations.append("Memory usage is within optimal range")
        
        return recommendations


# Global memory optimizer instance
memory_optimizer = MemoryOptimizer()
