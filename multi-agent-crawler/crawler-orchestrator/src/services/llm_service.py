"""
LLM Service for OpenRouter integration
Optimized for cost efficiency and resource management
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

import httpx
from loguru import logger
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage

from ..config import settings, LLM_MODELS
from .connection_pool import connection_pool
from .memory_optimizer import memory_optimizer


class TaskComplexity(Enum):
    """Task complexity levels for model selection"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"


@dataclass
class LLMRequest:
    """LLM request with batching support"""
    prompt: str
    system_prompt: Optional[str] = None
    complexity: TaskComplexity = TaskComplexity.MEDIUM
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None


class LLMService:
    """Service for interacting with LLMs via OpenRouter with optimization"""

    def __init__(self):
        self.api_key = settings.OPENROUTER_API_KEY
        self.base_url = "https://openrouter.ai/api/v1"
        self.default_model = settings.DEFAULT_MODEL
        self.fallback_model = settings.FALLBACK_MODEL
        self.client = None
        self.fallback_client = None
        self.request_queue = []
        self.batch_size = settings.BATCH_SIZE
        self.last_request_time = 0
        self.request_count = 0
        self.cost_tracking = {"total_tokens": 0, "estimated_cost": 0.0}
        
    async def initialize(self):
        """Initialize the LLM clients with primary and fallback models"""
        if not self.api_key:
            raise ValueError("OpenRouter API key not provided")

        # Primary client (Gemma 2 9B - cost efficient)
        self.client = ChatOpenAI(
            openai_api_key=self.api_key,
            openai_api_base=self.base_url,
            model_name=self.default_model,
            temperature=settings.TEMPERATURE,
            max_tokens=settings.MAX_TOKENS
        )

        # Fallback client (Gemma 2 27B - more capable)
        self.fallback_client = ChatOpenAI(
            openai_api_key=self.api_key,
            openai_api_base=self.base_url,
            model_name=self.fallback_model,
            temperature=settings.TEMPERATURE,
            max_tokens=settings.MAX_TOKENS
        )

        logger.info(f"✅ LLM Service initialized with primary: {self.default_model}, fallback: {self.fallback_model}")
    
    def select_model_by_complexity(self, complexity: TaskComplexity) -> ChatOpenAI:
        """Select appropriate model based on task complexity"""
        if complexity == TaskComplexity.COMPLEX:
            return self.fallback_client  # Use more capable model
        else:
            return self.client  # Use cost-efficient model

    async def generate_text(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        complexity: TaskComplexity = TaskComplexity.MEDIUM,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None
    ) -> str:
        """Generate text using optimized LLM with model selection"""
        try:
            if not self.client:
                await self.initialize()

            # Request throttling
            current_time = time.time()
            if current_time - self.last_request_time < settings.REQUEST_DELAY:
                await asyncio.sleep(settings.REQUEST_DELAY)

            self.last_request_time = current_time
            self.request_count += 1

            messages = []
            if system_prompt:
                messages.append(SystemMessage(content=system_prompt))
            messages.append(HumanMessage(content=prompt))

            # Select appropriate client based on complexity or model override
            if model:
                client = ChatOpenAI(
                    openai_api_key=self.api_key,
                    openai_api_base=self.base_url,
                    model_name=model,
                    temperature=temperature or settings.TEMPERATURE,
                    max_tokens=max_tokens or settings.MAX_TOKENS
                )
            else:
                client = self.select_model_by_complexity(complexity)
                if temperature or max_tokens:
                    # Create temporary client with custom parameters
                    model_name = self.fallback_model if complexity == TaskComplexity.COMPLEX else self.default_model
                    client = ChatOpenAI(
                        openai_api_key=self.api_key,
                        openai_api_base=self.base_url,
                        model_name=model_name,
                        temperature=temperature or settings.TEMPERATURE,
                        max_tokens=max_tokens or settings.MAX_TOKENS
                    )

            try:
                response = await client.agenerate([messages])
                result = response.generations[0][0].text.strip()

                # Track usage for cost estimation
                estimated_tokens = len(prompt.split()) + len(result.split())
                self.cost_tracking["total_tokens"] += estimated_tokens

                return result

            except Exception as primary_error:
                # Fallback to alternative model if primary fails
                if complexity != TaskComplexity.COMPLEX and self.fallback_client:
                    logger.warning(f"⚠️ Primary model failed, trying fallback: {primary_error}")
                    try:
                        response = await self.fallback_client.agenerate([messages])
                        return response.generations[0][0].text.strip()
                    except Exception as fallback_error:
                        logger.error(f"❌ Fallback model also failed: {fallback_error}")
                        raise fallback_error
                else:
                    raise primary_error

        except Exception as e:
            logger.error(f"❌ Error generating text: {e}")
            raise
    
    async def analyze_content(self, content: str, analysis_type: str = "general") -> Dict[str, Any]:
        """Analyze content using LLM"""
        system_prompt = f"""
        You are an expert content analyzer. Analyze the provided content and return insights.
        Analysis type: {analysis_type}
        
        Provide your analysis in the following JSON format:
        {{
            "summary": "Brief summary of the content",
            "key_points": ["list", "of", "key", "points"],
            "sentiment": "positive/negative/neutral",
            "topics": ["list", "of", "main", "topics"],
            "entities": ["list", "of", "important", "entities"],
            "insights": "Detailed insights and observations"
        }}
        """
        
        prompt = f"Content to analyze:\n\n{content[:4000]}..."  # Limit content length
        
        try:
            response = await self.generate_text(prompt, system_prompt)
            # Try to parse as JSON, fallback to text if it fails
            import json
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                return {"analysis": response}
        except Exception as e:
            logger.error(f"❌ Error analyzing content: {e}")
            return {"error": str(e)}
    
    async def extract_entities(self, text: str) -> List[Dict[str, str]]:
        """Extract named entities from text"""
        system_prompt = """
        Extract named entities from the provided text. Return them as a JSON list of objects.
        Each object should have 'entity', 'type', and 'context' fields.
        
        Entity types: PERSON, ORGANIZATION, LOCATION, DATE, MONEY, PRODUCT, EVENT, OTHER
        """
        
        prompt = f"Extract entities from this text:\n\n{text[:2000]}..."
        
        try:
            response = await self.generate_text(prompt, system_prompt)
            import json
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                return []
        except Exception as e:
            logger.error(f"❌ Error extracting entities: {e}")
            return []
    
    async def summarize_text(self, text: str, max_length: int = 200) -> str:
        """Summarize text to specified length"""
        system_prompt = f"""
        Summarize the provided text in approximately {max_length} words.
        Focus on the most important information and key insights.
        """
        
        prompt = f"Text to summarize:\n\n{text}"
        
        try:
            return await self.generate_text(prompt, system_prompt)
        except Exception as e:
            logger.error(f"❌ Error summarizing text: {e}")
            return "Error generating summary"
    
    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """Get list of available models"""
        return LLM_MODELS
    
    async def batch_generate(self, requests: List[LLMRequest]) -> List[str]:
        """Process multiple LLM requests in batches for efficiency"""
        results = []

        # Process in batches to manage resources
        for i in range(0, len(requests), self.batch_size):
            batch = requests[i:i + self.batch_size]

            # Process batch concurrently
            batch_tasks = []
            for request in batch:
                task = self.generate_text(
                    prompt=request.prompt,
                    system_prompt=request.system_prompt,
                    complexity=request.complexity,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature
                )
                batch_tasks.append(task)

            try:
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                # Handle exceptions in batch results
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"❌ Batch item failed: {result}")
                        results.append(f"Error: {str(result)}")
                    else:
                        results.append(result)

                # Small delay between batches
                if i + self.batch_size < len(requests):
                    await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"❌ Batch processing error: {e}")
                results.extend([f"Batch error: {str(e)}"] * len(batch))

        return results

    def get_cost_estimate(self) -> Dict[str, Any]:
        """Get estimated cost based on token usage"""
        # Rough cost estimates (these would need to be updated with actual pricing)
        cost_per_1k_tokens = {
            "google/gemma-2-9b-it": 0.0001,  # Very rough estimate
            "google/gemma-2-27b-it": 0.0003   # Very rough estimate
        }

        estimated_cost = (self.cost_tracking["total_tokens"] / 1000) * cost_per_1k_tokens.get(self.default_model, 0.0001)

        return {
            "total_tokens": self.cost_tracking["total_tokens"],
            "estimated_cost_usd": round(estimated_cost, 4),
            "total_requests": self.request_count,
            "average_tokens_per_request": self.cost_tracking["total_tokens"] / max(self.request_count, 1)
        }

    async def health_check(self) -> bool:
        """Check if the LLM service is healthy"""
        try:
            response = await self.generate_text(
                "Hello, are you working?",
                complexity=TaskComplexity.SIMPLE
            )
            return bool(response)
        except Exception as e:
            logger.error(f"❌ LLM health check failed: {e}")
            return False


# Global LLM service instance
llm_service = LLMService()
