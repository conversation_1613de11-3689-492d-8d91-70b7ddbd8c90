"""
Search Service integrating Exa API and Brave Search
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

import httpx
from loguru import logger
from exa_py import Exa

from ..config import settings


class SearchService:
    """Service for web search using multiple providers"""
    
    def __init__(self):
        self.exa_client = None
        self.brave_api_key = settings.BRAVE_API_KEY
        self.exa_api_key = settings.EXA_API_KEY
        
    async def initialize(self):
        """Initialize search clients"""
        if self.exa_api_key:
            self.exa_client = Exa(api_key=self.exa_api_key)
            logger.info("✅ Exa search client initialized")
        else:
            logger.warning("⚠️ Exa API key not provided")
            
        if not self.brave_api_key:
            logger.warning("⚠️ Brave API key not provided")
            
        logger.info("✅ Search Service initialized")
    
    async def exa_search(
        self,
        query: str,
        num_results: int = 10,
        include_domains: Optional[List[str]] = None,
        exclude_domains: Optional[List[str]] = None,
        start_crawl_date: Optional[datetime] = None,
        end_crawl_date: Optional[datetime] = None,
        use_autoprompt: bool = True,
        type: str = "neural"
    ) -> List[Dict[str, Any]]:
        """Search using Exa API"""
        if not self.exa_client:
            logger.error("❌ Exa client not initialized")
            return []
            
        try:
            search_params = {
                "query": query,
                "num_results": num_results,
                "use_autoprompt": use_autoprompt,
                "type": type,
                "include_text": True,
                "include_highlights": True
            }
            
            if include_domains:
                search_params["include_domains"] = include_domains
            if exclude_domains:
                search_params["exclude_domains"] = exclude_domains
            if start_crawl_date:
                search_params["start_crawl_date"] = start_crawl_date.isoformat()
            if end_crawl_date:
                search_params["end_crawl_date"] = end_crawl_date.isoformat()
            
            response = self.exa_client.search_and_contents(**search_params)
            
            results = []
            for result in response.results:
                results.append({
                    "title": result.title,
                    "url": result.url,
                    "text": result.text,
                    "highlights": getattr(result, 'highlights', []),
                    "published_date": getattr(result, 'published_date', None),
                    "author": getattr(result, 'author', None),
                    "score": getattr(result, 'score', 0),
                    "source": "exa"
                })
            
            logger.info(f"✅ Exa search completed: {len(results)} results for '{query}'")
            return results
            
        except Exception as e:
            logger.error(f"❌ Exa search error: {e}")
            return []
    
    async def brave_search(
        self,
        query: str,
        count: int = 10,
        offset: int = 0,
        country: str = "US",
        search_lang: str = "en",
        ui_lang: str = "en",
        safesearch: str = "moderate",
        freshness: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Search using Brave Search API"""
        if not self.brave_api_key:
            logger.error("❌ Brave API key not provided")
            return []
            
        try:
            url = "https://api.search.brave.com/res/v1/web/search"
            headers = {
                "Accept": "application/json",
                "Accept-Encoding": "gzip",
                "X-Subscription-Token": self.brave_api_key
            }
            
            params = {
                "q": query,
                "count": count,
                "offset": offset,
                "country": country,
                "search_lang": search_lang,
                "ui_lang": ui_lang,
                "safesearch": safesearch
            }
            
            if freshness:
                params["freshness"] = freshness
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response.raise_for_status()
                data = response.json()
            
            results = []
            if "web" in data and "results" in data["web"]:
                for result in data["web"]["results"]:
                    results.append({
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "description": result.get("description", ""),
                        "published_date": result.get("age", None),
                        "source": "brave"
                    })
            
            logger.info(f"✅ Brave search completed: {len(results)} results for '{query}'")
            return results
            
        except Exception as e:
            logger.error(f"❌ Brave search error: {e}")
            return []
    
    async def combined_search(
        self,
        query: str,
        num_results: int = 20,
        use_exa: bool = True,
        use_brave: bool = True,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Perform combined search using multiple providers"""
        tasks = []
        
        if use_exa and self.exa_client:
            tasks.append(self.exa_search(query, num_results // 2, **kwargs))
            
        if use_brave and self.brave_api_key:
            tasks.append(self.brave_search(query, num_results // 2, **kwargs))
        
        if not tasks:
            logger.error("❌ No search providers available")
            return []
        
        try:
            results_lists = await asyncio.gather(*tasks, return_exceptions=True)
            
            combined_results = []
            for results in results_lists:
                if isinstance(results, list):
                    combined_results.extend(results)
                else:
                    logger.error(f"❌ Search task failed: {results}")
            
            # Remove duplicates based on URL
            seen_urls = set()
            unique_results = []
            for result in combined_results:
                url = result.get("url", "")
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_results.append(result)
            
            logger.info(f"✅ Combined search completed: {len(unique_results)} unique results")
            return unique_results[:num_results]
            
        except Exception as e:
            logger.error(f"❌ Combined search error: {e}")
            return []
    
    async def semantic_search(
        self,
        query: str,
        num_results: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """Perform semantic search using Exa's neural search"""
        return await self.exa_search(
            query=query,
            num_results=num_results,
            type="neural",
            use_autoprompt=True
        )
    
    async def find_similar_content(
        self,
        url: str,
        num_results: int = 10
    ) -> List[Dict[str, Any]]:
        """Find content similar to a given URL using Exa"""
        if not self.exa_client:
            logger.error("❌ Exa client not initialized")
            return []
            
        try:
            response = self.exa_client.find_similar_and_contents(
                url=url,
                num_results=num_results,
                include_text=True
            )
            
            results = []
            for result in response.results:
                results.append({
                    "title": result.title,
                    "url": result.url,
                    "text": result.text,
                    "score": getattr(result, 'score', 0),
                    "source": "exa_similar"
                })
            
            logger.info(f"✅ Similar content search completed: {len(results)} results")
            return results
            
        except Exception as e:
            logger.error(f"❌ Similar content search error: {e}")
            return []


# Global search service instance
search_service = SearchService()
