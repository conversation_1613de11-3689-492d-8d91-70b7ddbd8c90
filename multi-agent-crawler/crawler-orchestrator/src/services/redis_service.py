"""
Redis Service for caching and task queue management
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

import aioredis
from loguru import logger

from ..config import settings


class RedisService:
    """Service for Redis operations"""
    
    def __init__(self):
        self.redis = None
        self.url = settings.REDIS_URL
        self.default_ttl = settings.CACHE_TTL
        
    async def connect(self):
        """Connect to Redis"""
        try:
            self.redis = await aioredis.from_url(
                self.url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test connection
            await self.redis.ping()
            logger.info("✅ Redis connection established")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis:
            await self.redis.close()
            logger.info("✅ Redis connection closed")
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None
    ) -> bool:
        """Set a value in Redis"""
        try:
            if not self.redis:
                await self.connect()
            
            # Serialize complex objects to JSON
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            ttl = ttl or self.default_ttl
            result = await self.redis.setex(key, ttl, value)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ Redis set error: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """Get a value from Redis"""
        try:
            if not self.redis:
                await self.connect()
            
            value = await self.redis.get(key)
            if value is None:
                return None
            
            # Try to deserialize JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logger.error(f"❌ Redis get error: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """Delete a key from Redis"""
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.delete(key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ Redis delete error: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if a key exists in Redis"""
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.exists(key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"❌ Redis exists error: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment a counter in Redis"""
        try:
            if not self.redis:
                await self.connect()
            
            result = await self.redis.incrby(key, amount)
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis increment error: {e}")
            return None
    
    async def add_to_list(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Add a value to a Redis list"""
        try:
            if not self.redis:
                await self.connect()
            
            # Serialize complex objects to JSON
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            await self.redis.lpush(key, value)
            
            if ttl:
                await self.redis.expire(key, ttl)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Redis list add error: {e}")
            return False
    
    async def get_list(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """Get values from a Redis list"""
        try:
            if not self.redis:
                await self.connect()
            
            values = await self.redis.lrange(key, start, end)
            
            # Try to deserialize JSON values
            result = []
            for value in values:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis list get error: {e}")
            return []
    
    async def add_to_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Add a value to a Redis set"""
        try:
            if not self.redis:
                await self.connect()
            
            # Serialize complex objects to JSON
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            await self.redis.sadd(key, value)
            
            if ttl:
                await self.redis.expire(key, ttl)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Redis set add error: {e}")
            return False
    
    async def get_set(self, key: str) -> List[Any]:
        """Get all values from a Redis set"""
        try:
            if not self.redis:
                await self.connect()
            
            values = await self.redis.smembers(key)
            
            # Try to deserialize JSON values
            result = []
            for value in values:
                try:
                    result.append(json.loads(value))
                except (json.JSONDecodeError, TypeError):
                    result.append(value)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Redis set get error: {e}")
            return []
    
    async def cache_search_results(
        self,
        query: str,
        results: List[Dict[str, Any]],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache search results"""
        cache_key = f"search:{hash(query)}"
        cache_data = {
            "query": query,
            "results": results,
            "cached_at": datetime.now().isoformat(),
            "count": len(results)
        }
        
        return await self.set(cache_key, cache_data, ttl)
    
    async def get_cached_search_results(self, query: str) -> Optional[Dict[str, Any]]:
        """Get cached search results"""
        cache_key = f"search:{hash(query)}"
        return await self.get(cache_key)
    
    async def cache_crawl_result(
        self,
        url: str,
        result: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """Cache crawl result"""
        cache_key = f"crawl:{hash(url)}"
        cache_data = {
            "url": url,
            "result": result,
            "cached_at": datetime.now().isoformat()
        }
        
        return await self.set(cache_key, cache_data, ttl)
    
    async def get_cached_crawl_result(self, url: str) -> Optional[Dict[str, Any]]:
        """Get cached crawl result"""
        cache_key = f"crawl:{hash(url)}"
        return await self.get(cache_key)
    
    async def store_research_mission(
        self,
        mission_id: str,
        mission_data: Dict[str, Any]
    ) -> bool:
        """Store research mission data"""
        mission_key = f"mission:{mission_id}"
        mission_data["stored_at"] = datetime.now().isoformat()
        
        return await self.set(mission_key, mission_data, ttl=86400)  # 24 hours
    
    async def get_research_mission(self, mission_id: str) -> Optional[Dict[str, Any]]:
        """Get research mission data"""
        mission_key = f"mission:{mission_id}"
        return await self.get(mission_key)
    
    async def add_task_to_queue(
        self,
        queue_name: str,
        task_data: Dict[str, Any]
    ) -> bool:
        """Add a task to a processing queue"""
        task_data["queued_at"] = datetime.now().isoformat()
        return await self.add_to_list(f"queue:{queue_name}", task_data)
    
    async def get_task_from_queue(self, queue_name: str) -> Optional[Dict[str, Any]]:
        """Get a task from a processing queue"""
        try:
            if not self.redis:
                await self.connect()
            
            # Use blocking pop to get task
            result = await self.redis.brpop(f"queue:{queue_name}", timeout=1)
            if result:
                _, task_data = result
                try:
                    return json.loads(task_data)
                except (json.JSONDecodeError, TypeError):
                    return {"data": task_data}
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Redis queue get error: {e}")
            return None
    
    async def get_queue_length(self, queue_name: str) -> int:
        """Get the length of a queue"""
        try:
            if not self.redis:
                await self.connect()
            
            return await self.redis.llen(f"queue:{queue_name}")
            
        except Exception as e:
            logger.error(f"❌ Redis queue length error: {e}")
            return 0
    
    async def clear_cache(self, pattern: str = "*") -> int:
        """Clear cache entries matching pattern"""
        try:
            if not self.redis:
                await self.connect()
            
            keys = await self.redis.keys(pattern)
            if keys:
                return await self.redis.delete(*keys)
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Redis clear cache error: {e}")
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get Redis statistics"""
        try:
            if not self.redis:
                await self.connect()
            
            info = await self.redis.info()
            
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0)
            }
            
        except Exception as e:
            logger.error(f"❌ Redis stats error: {e}")
            return {}


# Global Redis service instance
redis_service = RedisService()
