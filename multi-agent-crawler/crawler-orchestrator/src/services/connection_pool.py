"""
Connection Pool and Request Throttling Service
Optimized for 4 vCPU, 16GB RAM machine
"""

import asyncio
import time
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager
from dataclasses import dataclass

import httpx
import aioredis
from loguru import logger

from ..config import settings


@dataclass
class ConnectionStats:
    """Connection statistics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    active_connections: int = 0


class ConnectionPool:
    """Optimized connection pool with request throttling"""
    
    def __init__(self):
        self.http_client: Optional[httpx.AsyncClient] = None
        self.redis_pool: Optional[aioredis.ConnectionPool] = None
        self.semaphore = asyncio.Semaphore(settings.MAX_CONNECTIONS)
        self.request_times = []
        self.stats = ConnectionStats()
        self.last_request_time = 0.0
        
    async def initialize(self):
        """Initialize connection pools"""
        try:
            # HTTP client with connection pooling
            limits = httpx.Limits(
                max_keepalive_connections=settings.MAX_CONNECTIONS,
                max_connections=settings.MAX_CONNECTIONS * 2,
                keepalive_expiry=30.0
            )
            
            timeout = httpx.Timeout(
                connect=settings.CONNECTION_TIMEOUT,
                read=settings.CONNECTION_TIMEOUT,
                write=settings.CONNECTION_TIMEOUT,
                pool=settings.CONNECTION_TIMEOUT
            )
            
            self.http_client = httpx.AsyncClient(
                limits=limits,
                timeout=timeout,
                http2=True,
                follow_redirects=True
            )
            
            # Redis connection pool
            self.redis_pool = aioredis.ConnectionPool.from_url(
                settings.REDIS_URL,
                max_connections=settings.MAX_CONNECTIONS,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={
                    1: 1,  # TCP_KEEPIDLE
                    2: 3,  # TCP_KEEPINTVL
                    3: 5,  # TCP_KEEPCNT
                }
            )
            
            logger.info("✅ Connection pools initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize connection pools: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup connection pools"""
        if self.http_client:
            await self.http_client.aclose()
        
        if self.redis_pool:
            await self.redis_pool.disconnect()
        
        logger.info("✅ Connection pools cleaned up")
    
    @asynccontextmanager
    async def get_http_client(self):
        """Get HTTP client with connection limiting"""
        async with self.semaphore:
            # Request throttling
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < settings.REQUEST_DELAY:
                await asyncio.sleep(settings.REQUEST_DELAY - time_since_last)
            
            self.last_request_time = time.time()
            self.stats.active_connections += 1
            
            try:
                yield self.http_client
            finally:
                self.stats.active_connections -= 1
    
    @asynccontextmanager
    async def get_redis_connection(self):
        """Get Redis connection from pool"""
        redis = aioredis.Redis(connection_pool=self.redis_pool)
        try:
            yield redis
        finally:
            await redis.close()
    
    async def make_request(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> httpx.Response:
        """Make HTTP request with connection pooling and throttling"""
        start_time = time.time()
        
        try:
            async with self.get_http_client() as client:
                response = await client.request(method, url, **kwargs)
                
                # Update statistics
                self.stats.total_requests += 1
                self.stats.successful_requests += 1
                
                response_time = time.time() - start_time
                self.request_times.append(response_time)
                
                # Keep only last 100 response times for average calculation
                if len(self.request_times) > 100:
                    self.request_times = self.request_times[-100:]
                
                self.stats.average_response_time = sum(self.request_times) / len(self.request_times)
                
                return response
                
        except Exception as e:
            self.stats.total_requests += 1
            self.stats.failed_requests += 1
            logger.error(f"❌ Request failed: {e}")
            raise
    
    async def batch_requests(
        self,
        requests: list,
        batch_size: Optional[int] = None
    ) -> list:
        """Process requests in batches to optimize resource usage"""
        batch_size = batch_size or settings.BATCH_SIZE
        results = []
        
        for i in range(0, len(requests), batch_size):
            batch = requests[i:i + batch_size]
            
            # Process batch concurrently
            batch_tasks = []
            for request in batch:
                if isinstance(request, dict):
                    task = self.make_request(**request)
                else:
                    task = request
                batch_tasks.append(task)
            
            try:
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                results.extend(batch_results)
                
                # Small delay between batches to prevent overwhelming
                if i + batch_size < len(requests):
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"❌ Batch processing error: {e}")
                results.extend([e] * len(batch))
        
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        success_rate = 0.0
        if self.stats.total_requests > 0:
            success_rate = self.stats.successful_requests / self.stats.total_requests
        
        return {
            "total_requests": self.stats.total_requests,
            "successful_requests": self.stats.successful_requests,
            "failed_requests": self.stats.failed_requests,
            "success_rate": success_rate,
            "average_response_time": self.stats.average_response_time,
            "active_connections": self.stats.active_connections,
            "max_connections": settings.MAX_CONNECTIONS,
            "request_delay": settings.REQUEST_DELAY
        }
    
    async def health_check(self) -> bool:
        """Check connection pool health"""
        try:
            # Test HTTP client
            if self.http_client:
                async with self.get_http_client() as client:
                    response = await client.get("https://httpbin.org/status/200")
                    if response.status_code != 200:
                        return False
            
            # Test Redis connection
            if self.redis_pool:
                async with self.get_redis_connection() as redis:
                    await redis.ping()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Connection pool health check failed: {e}")
            return False


# Global connection pool instance
connection_pool = ConnectionPool()
