"""
CrewAI Orchestrator for Multi-Agent Crawling
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from crewai import Agent, Task, Crew, Process
from crewai_tools import BaseTool
from langchain_openai import ChatOpenAI
from loguru import logger

from ..config import settings, AGENT_ROLES
from ..services.llm_service import llm_service
from ..services.search_service import search_service
from ..services.crawler_service import crawler_service
from ..tools.custom_tools import (
    WebSearchTool,
    WebCrawlerTool,
    ContentAnalysisTool,
    DataCuratorTool,
    SemanticSearchTool
)


class CrewOrchestrator:
    """Orchestrates multi-agent crawling operations using CrewAI"""
    
    def __init__(self):
        self.llm = None
        self.agents = {}
        self.tools = {}
        self.crew = None
        
    async def initialize(self):
        """Initialize the crew orchestrator"""
        try:
            # Initialize LLM
            self.llm = ChatOpenAI(
                openai_api_key=settings.OPENROUTER_API_KEY,
                openai_api_base="https://openrouter.ai/api/v1",
                model_name=settings.DEFAULT_MODEL,
                temperature=settings.TEMPERATURE
            )
            
            # Initialize tools
            await self._initialize_tools()
            
            # Create agents
            self._create_agents()
            
            logger.info("✅ CrewAI Orchestrator initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize CrewAI Orchestrator: {e}")
            raise
    
    async def _initialize_tools(self):
        """Initialize custom tools"""
        self.tools = {
            "web_search": WebSearchTool(),
            "web_crawler": WebCrawlerTool(),
            "content_analysis": ContentAnalysisTool(),
            "data_curator": DataCuratorTool(),
            "semantic_search": SemanticSearchTool()
        }
        
        # Initialize tools that need async setup
        for tool in self.tools.values():
            if hasattr(tool, 'initialize'):
                await tool.initialize()
    
    def _create_agents(self):
        """Create specialized agents"""
        
        # Research Agent
        self.agents["researcher"] = Agent(
            role="Research Specialist",
            goal="Conduct comprehensive web research and gather relevant information",
            backstory="""You are an expert researcher with deep knowledge of web search 
            strategies and information gathering techniques. You excel at finding relevant 
            and high-quality sources on any topic.""",
            tools=[
                self.tools["web_search"],
                self.tools["web_crawler"],
                self.tools["semantic_search"]
            ],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Content Analyzer Agent
        self.agents["analyzer"] = Agent(
            role="Content Analysis Expert",
            goal="Analyze and extract meaningful insights from crawled content",
            backstory="""You are a skilled content analyst with expertise in natural 
            language processing, sentiment analysis, and information extraction. You can 
            identify key themes, entities, and insights from any type of content.""",
            tools=[
                self.tools["content_analysis"]
            ],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Data Curator Agent
        self.agents["curator"] = Agent(
            role="Data Organization Specialist",
            goal="Organize, clean, and structure collected data for optimal usability",
            backstory="""You are a meticulous data curator with expertise in data 
            organization, deduplication, and quality assurance. You ensure that all 
            collected information is properly structured and easily accessible.""",
            tools=[
                self.tools["data_curator"]
            ],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Report Generator Agent
        self.agents["reporter"] = Agent(
            role="Report Generation Expert",
            goal="Create comprehensive and insightful reports from analyzed data",
            backstory="""You are an expert report writer with the ability to synthesize 
            complex information into clear, actionable insights. You create reports that 
            are both comprehensive and easy to understand.""",
            tools=[],  # Uses LLM capabilities primarily
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
    
    async def execute_research_mission(
        self,
        topic: str,
        objectives: List[str],
        constraints: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a complete research mission"""
        try:
            logger.info(f"🚀 Starting research mission: {topic}")
            
            # Create tasks for the mission
            tasks = self._create_research_tasks(topic, objectives, constraints)
            
            # Create crew for this mission
            crew = Crew(
                agents=list(self.agents.values()),
                tasks=tasks,
                process=Process.sequential,
                verbose=True
            )
            
            # Execute the mission
            start_time = datetime.now()
            result = crew.kickoff()
            end_time = datetime.now()
            
            mission_result = {
                "topic": topic,
                "objectives": objectives,
                "result": result,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration": (end_time - start_time).total_seconds(),
                "success": True
            }
            
            logger.info(f"✅ Research mission completed: {topic}")
            return mission_result
            
        except Exception as e:
            logger.error(f"❌ Research mission failed: {e}")
            return {
                "topic": topic,
                "objectives": objectives,
                "error": str(e),
                "success": False
            }
    
    def _create_research_tasks(
        self,
        topic: str,
        objectives: List[str],
        constraints: Optional[Dict[str, Any]] = None
    ) -> List[Task]:
        """Create tasks for a research mission"""
        constraints = constraints or {}
        
        tasks = []
        
        # Research Task
        research_task = Task(
            description=f"""
            Conduct comprehensive research on the topic: {topic}
            
            Objectives:
            {chr(10).join(f"- {obj}" for obj in objectives)}
            
            Constraints:
            - Maximum {constraints.get('max_sources', 20)} sources
            - Focus on {constraints.get('content_type', 'all types')} of content
            - Time range: {constraints.get('time_range', 'any time')}
            
            Use web search and crawling tools to gather relevant information.
            Ensure sources are credible and diverse.
            """,
            agent=self.agents["researcher"],
            expected_output="A comprehensive list of relevant sources with extracted content"
        )
        tasks.append(research_task)
        
        # Analysis Task
        analysis_task = Task(
            description=f"""
            Analyze the content gathered by the researcher for the topic: {topic}
            
            Perform the following analysis:
            - Extract key themes and topics
            - Identify important entities and relationships
            - Analyze sentiment and tone
            - Summarize main findings
            - Identify gaps or areas needing more research
            
            Focus on insights that address the research objectives.
            """,
            agent=self.agents["analyzer"],
            expected_output="Detailed analysis with key insights, themes, and findings"
        )
        tasks.append(analysis_task)
        
        # Curation Task
        curation_task = Task(
            description=f"""
            Organize and structure the analyzed data for the topic: {topic}
            
            Tasks:
            - Remove duplicate information
            - Categorize content by relevance and type
            - Create a structured data format
            - Ensure data quality and consistency
            - Prepare data for report generation
            
            Output should be well-organized and easily accessible.
            """,
            agent=self.agents["curator"],
            expected_output="Clean, organized, and structured dataset ready for reporting"
        )
        tasks.append(curation_task)
        
        # Reporting Task
        reporting_task = Task(
            description=f"""
            Create a comprehensive report on the topic: {topic}
            
            The report should include:
            - Executive summary
            - Key findings and insights
            - Detailed analysis by category
            - Supporting evidence and sources
            - Conclusions and recommendations
            - Areas for further research
            
            Make the report clear, actionable, and well-structured.
            Address all research objectives.
            """,
            agent=self.agents["reporter"],
            expected_output="A comprehensive, well-structured research report"
        )
        tasks.append(reporting_task)
        
        return tasks
    
    async def execute_targeted_crawl(
        self,
        urls: List[str],
        extraction_rules: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute a targeted crawling operation"""
        try:
            logger.info(f"🕷️ Starting targeted crawl: {len(urls)} URLs")
            
            # Create crawling task
            crawl_task = Task(
                description=f"""
                Crawl the following URLs and extract relevant content:
                {chr(10).join(f"- {url}" for url in urls)}
                
                Extraction rules: {extraction_rules or 'Standard content extraction'}
                
                Ensure all content is properly extracted and structured.
                """,
                agent=self.agents["researcher"],
                expected_output="Extracted content from all specified URLs"
            )
            
            # Create analysis task
            analysis_task = Task(
                description="""
                Analyze the crawled content and extract insights.
                Focus on identifying patterns, key information, and relationships.
                """,
                agent=self.agents["analyzer"],
                expected_output="Analysis and insights from crawled content"
            )
            
            # Execute tasks
            crew = Crew(
                agents=[self.agents["researcher"], self.agents["analyzer"]],
                tasks=[crawl_task, analysis_task],
                process=Process.sequential,
                verbose=True
            )
            
            result = crew.kickoff()
            
            logger.info("✅ Targeted crawl completed")
            return {
                "urls": urls,
                "result": result,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"❌ Targeted crawl failed: {e}")
            return {
                "urls": urls,
                "error": str(e),
                "success": False
            }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        return {
            "agents": {
                name: {
                    "role": agent.role,
                    "goal": agent.goal,
                    "tools": [tool.__class__.__name__ for tool in agent.tools]
                }
                for name, agent in self.agents.items()
            },
            "tools": list(self.tools.keys()),
            "initialized": bool(self.llm and self.agents)
        }


# Global crew orchestrator instance
crew_orchestrator = CrewOrchestrator()
