"""
Real Estate Data Gathering Crew for Poland
This module orchestrates the multi-agent crew for gathering real estate data.
"""

from typing import List, Dict, Any
from crewai import Crew, Task
from dotenv import load_dotenv

from src.agents.real_estate_agents import RealEstateAgents
from src.services.llm_service import LLMService
from src.tools.real_estate_tools import (
    PolishAddressExtractor,
    PolishPhoneExtractor,
    CompanyNameExtractor,
    RealEstateDataExtractor
)

# Load environment variables
load_dotenv()

class RealEstateCrew:
    """Orchestrates a crew of agents for gathering real estate data across Poland."""
    
    def __init__(self, llm_service: LLMService = None):
        """Initialize the crew orchestrator with LLM service."""
        self.llm_service = llm_service or LLMService()
        self.tools = self._setup_tools()
        self.agents = self._setup_agents()
        
    def _setup_tools(self):
        """Set up the tools for the agents."""
        return [
            PolishAddressExtractor(),
            PolishPhoneExtractor(),
            CompanyNameExtractor(),
            RealEstateDataExtractor()
        ]
        
    def _setup_agents(self):
        """Set up the specialized agents."""
        agent_factory = RealEstateAgents(self.llm_service, self.tools)
        return {
            "discovery": agent_factory.create_discovery_agent(),
            "crawling": agent_factory.create_crawling_agent(),
            "extraction": agent_factory.create_extraction_agent(),
            "validation": agent_factory.create_validation_agent(),
            "storage": agent_factory.create_storage_agent(),
            "orchestration": agent_factory.create_orchestration_agent()
        }
        
    def create_tasks(self, target_region: str = "Poland", max_sites: int = 20):
        """Create tasks for the real estate data gathering process."""
        tasks = []
        
        # Task 1: Discover real estate data sources
        discovery_task = Task(
            description=f"""
            Identify the top {max_sites} most comprehensive sources of real estate and company data in {target_region}.
            Focus on:
            1. Official property registries and databases
            2. Major real estate listing websites
            3. Business directories with property information
            4. Construction and development company directories
            
            For each source, provide:
            - Website URL
            - Type of data available (properties, companies, both)
            - Estimated data coverage (national, regional)
            - Any access restrictions or anti-scraping measures
            
            Prioritize sources with the most comprehensive and structured data.
            """,
            expected_output="A prioritized list of real estate data sources with URLs and descriptions",
            agent=self.agents["discovery"]
        )
        tasks.append(discovery_task)
        
        # Task 2: Crawl identified sources
        crawling_task = Task(
            description="""
            Systematically crawl each of the identified real estate data sources.
            For each source:
            1. Navigate through pagination, search results, and listing pages
            2. Identify and follow links to detailed property or company pages
            3. Handle any anti-scraping measures appropriately
            4. Collect raw HTML content from relevant pages
            
            Ensure comprehensive coverage while respecting website terms of service.
            Implement appropriate delays between requests to avoid overloading servers.
            """,
            expected_output="Raw HTML content from crawled pages, organized by source",
            agent=self.agents["crawling"],
            context=[discovery_task]
        )
        tasks.append(crawling_task)
        
        # Task 3: Extract structured data
        extraction_task = Task(
            description="""
            Extract structured data from the crawled HTML content.
            For each page:
            1. Extract addresses using Polish address patterns
            2. Extract phone numbers using Polish phone number formats
            3. Extract company names from appropriate HTML elements
            4. Identify and extract other relevant property or company details
            
            Organize the extracted data into a structured format with clear relationships.
            """,
            expected_output="Structured data with addresses, phone numbers, company names, and other details",
            agent=self.agents["extraction"],
            context=[crawling_task]
        )
        tasks.append(extraction_task)
        
        # Task 4: Validate extracted data
        validation_task = Task(
            description="""
            Validate the accuracy and completeness of the extracted data.
            For each data point:
            1. Verify address formats and postal codes
            2. Validate phone number formats
            3. Check for missing or incomplete information
            4. Identify and flag potential duplicates
            5. Cross-reference information across multiple sources when possible
            
            Provide confidence scores for each validated data point.
            """,
            expected_output="Validated data with confidence scores and quality metrics",
            agent=self.agents["validation"],
            context=[extraction_task]
        )
        tasks.append(validation_task)
        
        # Task 5: Store and organize data
        storage_task = Task(
            description="""
            Organize and store the validated real estate data.
            1. Structure the data in a consistent format
            2. Create appropriate indexing for efficient retrieval
            3. Implement deduplication where necessary
            4. Organize data by region, property type, or other relevant categories
            5. Prepare data for export in multiple formats (CSV, JSON, database)
            
            Ensure the data is easily searchable and retrievable for future use.
            """,
            expected_output="Organized and indexed data ready for storage and retrieval",
            agent=self.agents["storage"],
            context=[validation_task]
        )
        tasks.append(storage_task)
        
        return tasks
        
    def build_crew(self, target_region: str = "Poland", max_sites: int = 20):
        """Build and return the real estate data gathering crew."""
        tasks = self.create_tasks(target_region, max_sites)
        
        crew = Crew(
            agents=list(self.agents.values()),
            tasks=tasks,
            verbose=True,
            process=self.llm_service.get_process()  # Sequential or hierarchical process
        )
        
        return crew
        
    def run(self, target_region: str = "Poland", max_sites: int = 20):
        """Run the real estate data gathering process."""
        crew = self.build_crew(target_region, max_sites)
        result = crew.kickoff()
        return result
