import os
from crewai import Agent
from langchain_openai import ChatOpenAI
from src.tools.real_estate_tools import PropertyResearchTool, PresentationCreatorTool

# Set up LLM with higher temperature for creative tasks
creative_llm = ChatOpenAI(
    model="gpt-4-turbo",
    temperature=0.7,
    openai_api_key=os.getenv("OPENAI_API_KEY")
)

def create_real_estate_researcher():
    return Agent(
        role="Senior Real Estate Researcher",
        goal="Find and analyze premium real estate properties matching luxury criteria",
        backstory=(
            "With over 10 years in high-end real estate, you specialize in identifying "
            "properties with exceptional potential for luxury clients."
        ),
        tools=[PropertyResearchTool()],
        verbose=True,
        llm=creative_llm
    )

def create_presentation_designer():
    return Agent(
        role="Elite Presentation Designer",
        goal="Create stunning, professional presentations for luxury real estate properties",
        backstory=(
            "Award-winning designer with expertise in creating visually stunning presentations "
            "for premium real estate brands. Your work has been featured in architectural digests."
        ),
        tools=[PresentationCreatorTool()],
        verbose=True,
        llm=creative_llm
    )

def create_market_analyst():
    return Agent(
        role="Real Estate Market Analyst",
        goal="Provide deep market insights and investment potential analysis",
        backstory=(
            "Former Wall Street analyst who transitioned to real estate, bringing sophisticated "
            "financial modeling and market trend analysis to property evaluation."
        ),
        verbose=True,
        llm=creative_llm
    )

def create_client_specialist():
    return Agent(
        role="Luxury Client Relations Specialist",
        goal="Understand and anticipate the needs of ultra-high-net-worth clients",
        backstory=(
            "Seasoned professional with experience serving billionaire clients across global "
            "luxury markets. Expert in discreet, personalized service."
        ),
        verbose=True,
        llm=creative_llm
    )
