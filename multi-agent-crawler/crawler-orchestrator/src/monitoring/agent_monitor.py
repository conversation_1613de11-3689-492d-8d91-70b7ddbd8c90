"""
Agent Monitoring System for CrewAI Agents
Real-time monitoring and performance tracking
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from loguru import logger


class AgentStatus(Enum):
    """Agent status enumeration"""
    IDLE = "idle"
    ACTIVE = "active"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    agent_id: str
    agent_name: str
    status: AgentStatus
    tasks_completed: int = 0
    tasks_failed: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    success_rate: float = 0.0
    last_activity: Optional[datetime] = None
    current_task: Optional[str] = None
    error_count: int = 0
    last_error: Optional[str] = None


@dataclass
class TaskMetrics:
    """Task execution metrics"""
    task_id: str
    agent_id: str
    task_type: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    result: Optional[Any] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = None


class AgentMonitor:
    """Real-time agent monitoring system"""
    
    def __init__(self):
        self.agents: Dict[str, AgentMetrics] = {}
        self.tasks: Dict[str, TaskMetrics] = {}
        self.task_history: List[TaskMetrics] = []
        self.monitoring_active = False
        self.update_interval = 5.0  # seconds
        
        # Initialize default agents
        self._initialize_default_agents()
    
    def _initialize_default_agents(self):
        """Initialize default agent metrics"""
        default_agents = [
            ("researcher", "Research Agent"),
            ("analyzer", "Content Analyzer"),
            ("curator", "Data Curator"),
            ("reporter", "Report Generator")
        ]
        
        for agent_id, agent_name in default_agents:
            self.agents[agent_id] = AgentMetrics(
                agent_id=agent_id,
                agent_name=agent_name,
                status=AgentStatus.IDLE
            )
    
    def start_monitoring(self):
        """Start the monitoring system"""
        self.monitoring_active = True
        logger.info("🔍 Agent monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring system"""
        self.monitoring_active = False
        logger.info("🛑 Agent monitoring stopped")
    
    def register_agent(self, agent_id: str, agent_name: str):
        """Register a new agent for monitoring"""
        if agent_id not in self.agents:
            self.agents[agent_id] = AgentMetrics(
                agent_id=agent_id,
                agent_name=agent_name,
                status=AgentStatus.IDLE
            )
            logger.info(f"📝 Registered agent: {agent_name} ({agent_id})")
    
    def update_agent_status(self, agent_id: str, status: AgentStatus, current_task: Optional[str] = None):
        """Update agent status"""
        if agent_id in self.agents:
            self.agents[agent_id].status = status
            self.agents[agent_id].last_activity = datetime.now()
            if current_task:
                self.agents[agent_id].current_task = current_task
            
            logger.debug(f"🔄 Agent {agent_id} status updated: {status.value}")
    
    def start_task(self, task_id: str, agent_id: str, task_type: str, metadata: Optional[Dict] = None) -> TaskMetrics:
        """Start tracking a new task"""
        task = TaskMetrics(
            task_id=task_id,
            agent_id=agent_id,
            task_type=task_type,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            metadata=metadata or {}
        )
        
        self.tasks[task_id] = task
        self.update_agent_status(agent_id, AgentStatus.ACTIVE, task_type)
        
        logger.info(f"🚀 Task started: {task_id} ({task_type}) by {agent_id}")
        return task
    
    def complete_task(self, task_id: str, result: Optional[Any] = None):
        """Mark a task as completed"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()
            task.execution_time = (task.end_time - task.start_time).total_seconds()
            task.result = result
            
            # Update agent metrics
            agent = self.agents.get(task.agent_id)
            if agent:
                agent.tasks_completed += 1
                agent.total_execution_time += task.execution_time
                agent.average_execution_time = agent.total_execution_time / agent.tasks_completed
                agent.success_rate = agent.tasks_completed / (agent.tasks_completed + agent.tasks_failed)
                agent.status = AgentStatus.IDLE
                agent.current_task = None
            
            # Move to history
            self.task_history.append(task)
            del self.tasks[task_id]
            
            logger.info(f"✅ Task completed: {task_id} in {task.execution_time:.2f}s")
    
    def fail_task(self, task_id: str, error: str):
        """Mark a task as failed"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.status = TaskStatus.FAILED
            task.end_time = datetime.now()
            task.execution_time = (task.end_time - task.start_time).total_seconds()
            task.error = error
            
            # Update agent metrics
            agent = self.agents.get(task.agent_id)
            if agent:
                agent.tasks_failed += 1
                agent.error_count += 1
                agent.last_error = error
                agent.success_rate = agent.tasks_completed / (agent.tasks_completed + agent.tasks_failed)
                agent.status = AgentStatus.ERROR
                agent.current_task = None
            
            # Move to history
            self.task_history.append(task)
            del self.tasks[task_id]
            
            logger.error(f"❌ Task failed: {task_id} - {error}")
    
    def get_agent_metrics(self, agent_id: str) -> Optional[AgentMetrics]:
        """Get metrics for a specific agent"""
        return self.agents.get(agent_id)
    
    def get_all_agent_metrics(self) -> Dict[str, AgentMetrics]:
        """Get metrics for all agents"""
        return self.agents.copy()
    
    def get_active_tasks(self) -> Dict[str, TaskMetrics]:
        """Get all currently active tasks"""
        return self.tasks.copy()
    
    def get_task_history(self, limit: int = 100) -> List[TaskMetrics]:
        """Get recent task history"""
        return self.task_history[-limit:]
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get overall system statistics"""
        total_tasks = sum(agent.tasks_completed + agent.tasks_failed for agent in self.agents.values())
        total_completed = sum(agent.tasks_completed for agent in self.agents.values())
        total_failed = sum(agent.tasks_failed for agent in self.agents.values())
        
        overall_success_rate = total_completed / total_tasks if total_tasks > 0 else 0.0
        
        active_agents = sum(1 for agent in self.agents.values() if agent.status == AgentStatus.ACTIVE)
        idle_agents = sum(1 for agent in self.agents.values() if agent.status == AgentStatus.IDLE)
        error_agents = sum(1 for agent in self.agents.values() if agent.status == AgentStatus.ERROR)
        
        return {
            "total_agents": len(self.agents),
            "active_agents": active_agents,
            "idle_agents": idle_agents,
            "error_agents": error_agents,
            "total_tasks": total_tasks,
            "completed_tasks": total_completed,
            "failed_tasks": total_failed,
            "active_tasks": len(self.tasks),
            "overall_success_rate": overall_success_rate,
            "monitoring_active": self.monitoring_active
        }
    
    def get_performance_trends(self, hours: int = 24) -> Dict[str, List[Dict]]:
        """Get performance trends over time"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter recent tasks
        recent_tasks = [
            task for task in self.task_history
            if task.start_time >= cutoff_time
        ]
        
        # Group by hour
        hourly_stats = {}
        for task in recent_tasks:
            hour_key = task.start_time.strftime("%Y-%m-%d %H:00")
            if hour_key not in hourly_stats:
                hourly_stats[hour_key] = {
                    "completed": 0,
                    "failed": 0,
                    "total_time": 0.0,
                    "avg_time": 0.0
                }
            
            if task.status == TaskStatus.COMPLETED:
                hourly_stats[hour_key]["completed"] += 1
                if task.execution_time:
                    hourly_stats[hour_key]["total_time"] += task.execution_time
            elif task.status == TaskStatus.FAILED:
                hourly_stats[hour_key]["failed"] += 1
        
        # Calculate averages
        for stats in hourly_stats.values():
            if stats["completed"] > 0:
                stats["avg_time"] = stats["total_time"] / stats["completed"]
        
        return {
            "hourly_stats": [
                {"hour": hour, **stats}
                for hour, stats in sorted(hourly_stats.items())
            ]
        }
    
    def export_metrics(self) -> Dict[str, Any]:
        """Export all metrics for external analysis"""
        return {
            "timestamp": datetime.now().isoformat(),
            "agents": {
                agent_id: asdict(metrics)
                for agent_id, metrics in self.agents.items()
            },
            "active_tasks": {
                task_id: asdict(task)
                for task_id, task in self.tasks.items()
            },
            "task_history": [
                asdict(task) for task in self.task_history[-1000:]  # Last 1000 tasks
            ],
            "system_stats": self.get_system_stats()
        }
    
    def simulate_activity(self):
        """Simulate agent activity for demo purposes"""
        import random
        
        agents = list(self.agents.keys())
        task_types = ["search", "crawl", "analyze", "report", "extract", "verify"]
        
        # Simulate some completed tasks
        for i in range(50):
            agent_id = random.choice(agents)
            task_type = random.choice(task_types)
            task_id = f"sim_task_{i}"
            
            # Create a simulated task
            start_time = datetime.now() - timedelta(
                hours=random.randint(0, 24),
                minutes=random.randint(0, 59)
            )
            
            task = TaskMetrics(
                task_id=task_id,
                agent_id=agent_id,
                task_type=task_type,
                status=TaskStatus.COMPLETED if random.random() > 0.1 else TaskStatus.FAILED,
                start_time=start_time,
                end_time=start_time + timedelta(seconds=random.randint(10, 300)),
                execution_time=random.randint(10, 300)
            )
            
            if task.status == TaskStatus.COMPLETED:
                task.result = f"Simulated result for {task_type}"
            else:
                task.error = f"Simulated error for {task_type}"
            
            self.task_history.append(task)
            
            # Update agent metrics
            agent = self.agents[agent_id]
            if task.status == TaskStatus.COMPLETED:
                agent.tasks_completed += 1
                agent.total_execution_time += task.execution_time
            else:
                agent.tasks_failed += 1
                agent.error_count += 1
            
            if agent.tasks_completed + agent.tasks_failed > 0:
                agent.success_rate = agent.tasks_completed / (agent.tasks_completed + agent.tasks_failed)
                agent.average_execution_time = agent.total_execution_time / max(agent.tasks_completed, 1)
        
        logger.info("🎭 Simulated agent activity generated")


# Global monitor instance
agent_monitor = AgentMonitor()
