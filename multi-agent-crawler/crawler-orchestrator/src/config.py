"""
Configuration settings for the Multi-Agent Crawler Orchestrator
"""

import os
from typing import Optional

from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # API Keys
    OPENROUTER_API_KEY: str = os.getenv("OPENROUTER_API_KEY", "")
    EXA_API_KEY: str = os.getenv("EXA_API_KEY", "")
    BRAVE_API_KEY: str = os.getenv("BRAVE_API_KEY", "")
    
    # Database URLs
    NOCODB_URL: str = os.getenv("NOCODB_URL", "http://localhost:8080")
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # Crawler settings (optimized for 4 vCPU, 16GB RAM)
    MAX_CONCURRENT_CRAWLS: int = int(os.getenv("MAX_CONCURRENT_CRAWLS", "3"))
    CRAWL_TIMEOUT: int = int(os.getenv("CRAWL_TIMEOUT", "180"))
    USER_AGENT: str = os.getenv("USER_AGENT", "MultiAgentCrawler/1.0")

    # LLM settings (optimized for cost and performance)
    DEFAULT_MODEL: str = os.getenv("DEFAULT_MODEL", "google/gemma-2-9b-it")
    FALLBACK_MODEL: str = os.getenv("FALLBACK_MODEL", "google/gemma-2-27b-it")
    TEMPERATURE: float = float(os.getenv("TEMPERATURE", "0.7"))
    MAX_TOKENS: int = int(os.getenv("MAX_TOKENS", "2000"))

    # Connection pooling and throttling
    MAX_CONNECTIONS: int = int(os.getenv("MAX_CONNECTIONS", "10"))
    CONNECTION_TIMEOUT: int = int(os.getenv("CONNECTION_TIMEOUT", "30"))
    REQUEST_DELAY: float = float(os.getenv("REQUEST_DELAY", "0.5"))
    BATCH_SIZE: int = int(os.getenv("BATCH_SIZE", "5"))

    # Memory optimization
    STREAMING_THRESHOLD: int = int(os.getenv("STREAMING_THRESHOLD", "1000000"))  # 1MB
    MAX_MEMORY_USAGE: float = float(os.getenv("MAX_MEMORY_USAGE", "0.8"))  # 80% of available
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/crawler.log")
    
    # Data storage (optimized caching)
    DATA_DIR: str = os.getenv("DATA_DIR", "./data")
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "7200"))  # 2 hours for better performance
    CACHE_MAX_SIZE: int = int(os.getenv("CACHE_MAX_SIZE", "1000"))  # Max cached items
    
    # Application settings
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# LLM Model configurations
LLM_MODELS = {
    "gemma-2-9b": {
        "name": "google/gemma-2-9b-it",
        "max_tokens": 8192,
        "description": "Gemma 2 9B - Fast and efficient"
    },
    "gemma-2-27b": {
        "name": "google/gemma-2-27b-it", 
        "max_tokens": 8192,
        "description": "Gemma 2 27B - More capable"
    },
    "llama-3.1-8b": {
        "name": "meta-llama/llama-3.1-8b-instruct",
        "max_tokens": 131072,
        "description": "Llama 3.1 8B - Long context"
    }
}


# Agent roles and descriptions
AGENT_ROLES = {
    "researcher": {
        "name": "Research Agent",
        "description": "Conducts web research and gathers information",
        "tools": ["exa_search", "brave_search", "web_crawler"]
    },
    "analyzer": {
        "name": "Content Analyzer",
        "description": "Analyzes and extracts insights from crawled content",
        "tools": ["content_analysis", "sentiment_analysis", "entity_extraction"]
    },
    "curator": {
        "name": "Data Curator",
        "description": "Organizes and structures collected data",
        "tools": ["data_cleaning", "categorization", "deduplication"]
    },
    "reporter": {
        "name": "Report Generator",
        "description": "Creates comprehensive reports from analyzed data",
        "tools": ["report_generation", "visualization", "summary_creation"]
    }
}
