#!/usr/bin/env python3
"""
Multi-Agent Crawler Orchestrator
Main entry point for the application
"""

import asyncio
import os
import threading
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from loguru import logger

from src.api.routes import router
from src.config import settings
from src.services.redis_service import RedisService
from src.ui.gradio_interface import create_gradio_interface


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("🚀 Starting Multi-Agent Crawler Orchestrator")
    
    # Initialize Redis connection
    redis_service = RedisService()
    await redis_service.connect()
    app.state.redis = redis_service
    
    logger.info("✅ Application startup complete")
    
    yield
    
    # Cleanup
    logger.info("🛑 Shutting down application")
    await redis_service.disconnect()


# Create FastAPI app
app = FastAPI(
    title="Multi-Agent Crawler Orchestrator",
    description="Advanced web crawling with AI agents",
    version="1.0.0",
    lifespan=lifespan
)

# Include API routes
app.include_router(router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Multi-Agent Crawler Orchestrator",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


def run_gradio():
    """Run Gradio interface in a separate thread"""
    gradio_app = create_gradio_interface()
    gradio_app.launch(
        server_name="0.0.0.0",
        server_port=8001,
        share=False
    )


def run_streamlit():
    """Run Streamlit interface in a separate thread"""
    import subprocess
    import sys

    # Run Streamlit app
    subprocess.run([
        sys.executable, "-m", "streamlit", "run",
        "streamlit_app.py",
        "--server.port=8003",
        "--server.address=0.0.0.0",
        "--server.headless=true"
    ])


def run_tablet_interface():
    """Run Tablet interface in a separate thread"""
    import subprocess
    import sys

    # Run Tablet Streamlit app
    subprocess.run([
        sys.executable, "-m", "streamlit", "run",
        "tablet_app.py",
        "--server.port=8004",
        "--server.address=0.0.0.0",
        "--server.headless=true"
    ])


def main():
    """Main function"""
    logger.info("🚀 Starting Multi-Agent Crawler Orchestrator")

    # Start Gradio in a separate thread
    gradio_thread = threading.Thread(target=run_gradio, daemon=True)
    gradio_thread.start()
    logger.info("✅ Gradio interface started on port 8001")

    # Start Streamlit control panel in a separate thread
    streamlit_thread = threading.Thread(target=run_streamlit, daemon=True)
    streamlit_thread.start()
    logger.info("✅ Streamlit control panel started on port 8003")

    # Start Tablet interface in a separate thread
    tablet_thread = threading.Thread(target=run_tablet_interface, daemon=True)
    tablet_thread.start()
    logger.info("✅ Tablet interface started on port 8004")

    # Start FastAPI server
    logger.info("🔧 Starting FastAPI server on port 8000")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )


if __name__ == "__main__":
    main()
