{"name": "summarize_slack_conversation", "description": "Use this tool to get a summary of a Slack conversation in a channel.", "color": "linear-gradient(rgb(235,70,180), rgb(87,203,87))", "iconSrc": "", "schema": "[{\"id\":0,\"property\":\"channel\",\"description\":\"The channel name to summarize\",\"type\":\"string\",\"required\":false}]", "func": "/*\n* You can use any libraries imported in Flowise\n* You can use properties specified in Input Schema as variables. Ex: Property = userid, Variable = $userid\n* You can get default flow config: $flow.sessionId, $flow.chatId, $flow.chatflowId, $flow.input, $flow.state\n* You can get custom variables: $vars.<variable-name>\n* Must return a string value at the end of function\n*/\n\nconst fetch = require('node-fetch');\nconst url = 'Your n8n Webhook URL';\nconst options = {\n    method: 'GET',\n    headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${$vars.headerauth}`\n    }\n};\ntry {\n    const response = await fetch(url, options);\n    const text = await response.text();\n    return text;\n} catch (error) {\n    console.error(error);\n    return '';\n}"}