{"name": "supabase", "description": "The open source Firebase alternative.", "version": "0.0.0", "author": "Supabase, Inc.", "license": "Apache-2.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "build": "turbo run build", "build:studio": "turbo run build --filter=studio", "build:design-system": "turbo run build --filter=design-system", "build:docs": "turbo run build --filter=docs", "clean": "turbo run clean --parallel && rimraf -G node_modules/{*,.bin,.modules.yaml}", "dev": "turbo run dev --parallel", "dev:reference": "turbo run dev --filter=reference --parallel", "dev:studio": "turbo run dev --filter=studio --parallel", "dev:studio-local": "pnpm run --dir tests/studio-tests codegen:setup", "dev:docs": "turbo run dev --filter=docs --parallel", "dev:www": "turbo run dev --filter=www --parallel", "dev:design-system": "turbo run dev --filter=design-system --parallel", "lint": "turbo run lint", "typecheck": "turbo --continue typecheck", "test:prettier": "prettier -c '{apps,packages}/**/*.{js,jsx,ts,tsx,css,md,mdx,json}'", "format": "prettier --write '{apps,packages}/**/*.{js,jsx,ts,tsx,css,md,mdx,json}'", "test:docs": "turbo run test --filter=docs", "test:ui": "turbo run test --filter=ui", "test:ui-patterns": "turbo run test --filter=ui-patterns", "test:studio": "turbo run test --filter=studio", "test:studio:watch": "turbo run test --filter=studio -- watch", "test:e2e:studio-local": "pnpm --prefix tests/studio-tests run test:local", "test:e2e:studio-staging": "pnpm --prefix tests/studio-tests run test:staging", "perf:kong": "ab -t 5 -c 20 -T application/json http://localhost:8000/", "perf:meta": "ab -t 5 -c 20 -T application/json http://localhost:5555/tables", "generate:types": "supabase gen types typescript --local > ./supabase/functions/common/database-types.ts", "api:codegen": "cd packages/api-types && pnpm run codegen", "knip": "pnpx knip@~5.50.0"}, "devDependencies": {"@aws-sdk/client-secrets-manager": "^3.410.0", "@types/node": "catalog:", "eslint": "^8.57.0", "prettier": "3.2.4", "prettier-plugin-sql-cst": "^0.11.0", "rimraf": "^6.0.0", "sass": "^1.72.0", "supabase": "^1.151.1", "supports-color": "^8.0.0", "turbo": "2.3.3", "typescript": "~5.5.0"}, "repository": {"type": "git", "url": "git+https://github.com/supabase/supabase.git"}, "pnpm": {"overrides": {"@supabase/supabase-js>@supabase/auth-js": "2.69.0-rc.3", "vinxi>esbuild": "0.25.2", "@tanstack/directive-functions-plugin>vite": "6.2.6", "@tanstack/react-start-plugin>vite": "6.2.6"}, "neverBuiltDependencies": ["libpg-query"]}, "engines": {"pnpm": ">=9", "node": ">=20"}, "keywords": ["postgres", "firebase", "storage", "functions", "database", "auth"], "packageManager": "pnpm@9.15.5"}