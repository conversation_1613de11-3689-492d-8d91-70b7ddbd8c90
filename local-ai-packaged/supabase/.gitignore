.DS_Store
node_modules
.next
out
.docz
tmp
*.swp

coverage
allure-results
allure-report
.nyc_output
*.log

# turbo
.turbo

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
# excempt the apps/www .env
!apps/www/.env
.env.tmp
.env.test
.env.local
.env.staging
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

.vscode
.idea
.vercel

#include template .env file for docker-compose
!docker/.env
!docker/supabase/.env
!docker/supabase-traefik/.env

# Supabase
**/supabase/.branches
**/supabase/.temp

apps/new-docs/*

# UI tokens
packages/ui/tokens/**/*.json

# For self-hosted logs: https://github.com/supabase/supabase/blob/86e3ab20abfdb9c3e666334d3d2f8efeef9ccf2c/docker/docker-compose-logging.yml#L101
gcloud.json

# sitemaps
# apps/www/public/*.xml
# apps/docs/public/*.xml

# CLI version file
.temp/cli-latest

.pnpm-store/*

# Sentry CLI config
**/.sentryclirc
